export function waitFor(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
export function waitForSync(ms: number) {
  const start = Date.now();
  let now = start;
  while (now - start < ms) {
    now = Date.now();
  }
}

export function capitalizeFirstLetter(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export const formatDateInLocalFormat = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export const removeEmptyFieldsFromObject = <T extends Record<string, unknown>>(
  obj: T
) => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([_, v]) => v !== null && v !== undefined && v !== ""
    )
  ) as T;
};

export function getCurrentDate(
  format: "YYYY-MM-DD" | "DD-MM-YYYY" = "YYYY-MM-DD"
): string {
  switch (format) {
    case "DD-MM-YYYY":
      return new Date().toLocaleDateString("en-GB");
    default:
      return new Date().toISOString().slice(0, 10);
  }
}

export function convertTimeInputToDateTime(
  timeValue: string,
  date: Date = new Date()
): string {
  const [hours, minutes] = timeValue.split(":").map(Number);

  if (!hours || !minutes) {
    throw new Error("Invalid time format");
  }

  const localDateTime = new Date(date);
  localDateTime.setHours(hours);
  localDateTime.setMinutes(minutes);
  localDateTime.setSeconds(0);
  localDateTime.setMilliseconds(0);

  return localDateTime.toISOString(); // Returns in UTC with 'Z' suffix
}
