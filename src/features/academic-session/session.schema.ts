import { dateSchema } from "@/common/schemas/zod-common.schemas";
import { z } from "zod";

export const createSessionSchema = z
  .object({
    name: z.string().nonempty({ message: "Session name is required" }),
    startDate: dateSchema,
    endDate: dateSchema,
    isActive: z.boolean().default(true),
  })
  .refine((data) => data.startDate < data.endDate, {
    message: "Start date must be before end date",
    path: ["startDate"],
  });
