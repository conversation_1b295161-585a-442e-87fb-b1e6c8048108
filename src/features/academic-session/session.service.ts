// src/pages/OnBoarding/Sessions/session.service.ts
import { sendApiRequest } from "@/common/services/api.service";
import type {
  AcademicSession,
  CreateAcademicSessionPayload,
} from "./session.type";
import { logger } from "@/lib/logger";

interface CreateAcademicSessionParam {
  branchId: string;
  payload: CreateAcademicSessionPayload;
}
export async function createAcademicSession({
  branchId,
  payload,
}: CreateAcademicSessionParam) {
  try {
    return await sendApiRequest<AcademicSession>(
      `/branches/${branchId}/sessions`,
      {
        method: "POST",
        data: payload,
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Error creating academic session", error);
    throw error;
  }
}

interface FetchAllAcademicSessionParam {
  branchId: string;
}
export async function fetchAllAcademicSessions({
  branchId,
}: FetchAllAcademicSessionParam) {
  try {
    return await sendApiRequest<AcademicSession[]>(
      `/branches/${branchId}/sessions`,
      {
        method: "GET",
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching academic sessions", error);
    throw error;
  }
}
