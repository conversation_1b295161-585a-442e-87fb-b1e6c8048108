import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createAcademicSession,
  fetchAllAcademicSessions,
} from "./session.service";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import { waitFor } from "@/utils/utils";

function getAcademicSessionQueryKey(branchId?: string) {
  return ["sessions", branchId];
}

export function useAcademicSessions(branchId?: string) {
  return useQuery({
    queryKey: getAcademicSessionQueryKey(branchId),
    queryFn: async () => {
      await waitFor(2000);
      const sessions = await fetchAllAcademicSessions({ branchId: branchId! });
      return sessions;
    },
    enabled: Bo<PERSON>an(branchId),
  });
}

export function useCreateAcademicSession(branchId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createAcademicSession,
    onSuccess: async () => {
      notifyResourceActionSuccess("Academic Session", "create");
      await queryClient.invalidateQueries({
        queryKey: getAcademicSessionQueryKey(branchId),
      });
    },
  });
}
