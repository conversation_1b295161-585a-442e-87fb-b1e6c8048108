import { motion } from "framer-motion";
import { 
  ChartBarIcon, 
  TrendingUpIcon, 
  UserGroupIcon,
  AcademicCapIcon,
  BookOpenIcon,
  DocumentCheckIcon 
} from "@heroicons/react/24/outline";

interface QuickStatsProps {
  stats: {
    totalStudents: number;
    totalStaff: number;
    totalSubjects: number;
    totalClasses: number;
    totalSections: number;
    totalExams: number;
    totalAssignments: number;
    staffByDepartment: Record<string, number>;
  };
  isLoading?: boolean;
}

export const QuickStats = ({ stats, isLoading }: QuickStatsProps) => {
  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-32 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="skeleton h-12 w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const quickStatsData = [
    {
      label: "Student-Teacher Ratio",
      value: stats.totalStaff > 0 ? `${Math.round(stats.totalStudents / stats.totalStaff)}:1` : "N/A",
      icon: <UserGroupIcon className="w-5 h-5" />,
      color: "text-primary",
      bgColor: "bg-primary/10",
    },
    {
      label: "Avg Students/Class",
      value: stats.totalClasses > 0 ? Math.round(stats.totalStudents / stats.totalClasses) : 0,
      icon: <AcademicCapIcon className="w-5 h-5" />,
      color: "text-secondary",
      bgColor: "bg-secondary/10",
    },
    {
      label: "Subjects per Class",
      value: stats.totalClasses > 0 ? Math.round(stats.totalSubjects / stats.totalClasses) : 0,
      icon: <BookOpenIcon className="w-5 h-5" />,
      color: "text-accent",
      bgColor: "bg-accent/10",
    },
    {
      label: "Assignment Coverage",
      value: stats.totalSubjects > 0 ? `${Math.round((stats.totalAssignments / (stats.totalSubjects * stats.totalSections)) * 100)}%` : "0%",
      icon: <DocumentCheckIcon className="w-5 h-5" />,
      color: "text-success",
      bgColor: "bg-success/10",
    },
  ];

  const departmentStats = [
    {
      department: "Academic",
      count: stats.staffByDepartment.ACADEMIC ?? 0,
      percentage: stats.totalStaff > 0 ? ((stats.staffByDepartment.ACADEMIC ?? 0) / stats.totalStaff) * 100 : 0,
      color: "text-primary",
    },
    {
      department: "Admin",
      count: stats.staffByDepartment.ADMINISTRATION ?? 0,
      percentage: stats.totalStaff > 0 ? ((stats.staffByDepartment.ADMINISTRATION ?? 0) / stats.totalStaff) * 100 : 0,
      color: "text-secondary",
    },
    {
      department: "Support",
      count: stats.staffByDepartment.SUPPORT ?? 0,
      percentage: stats.totalStaff > 0 ? ((stats.staffByDepartment.SUPPORT ?? 0) / stats.totalStaff) * 100 : 0,
      color: "text-accent",
    },
  ];

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
          <ChartBarIcon className="w-5 h-5 mr-2 text-primary" />
          Quick Insights
        </h3>

        <div className="space-y-4">
          {/* Key Metrics */}
          <div className="space-y-3">
            {quickStatsData.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="flex items-center justify-between p-3 rounded-lg bg-base-200/50"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${stat.bgColor} ${stat.color}`}>
                    {stat.icon}
                  </div>
                  <div className="text-sm font-medium text-base-content">
                    {stat.label}
                  </div>
                </div>
                <div className={`text-lg font-bold ${stat.color}`}>
                  {stat.value}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Department Distribution */}
          <div className="pt-4 border-t border-base-300">
            <h4 className="font-medium text-base-content/80 mb-3">Staff Distribution</h4>
            <div className="space-y-2">
              {departmentStats.map((dept, index) => (
                <motion.div
                  key={dept.department}
                  className="flex items-center justify-between"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full bg-${dept.color.split('-')[1]}`}></div>
                    <span className="text-sm text-base-content/70">{dept.department}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-base-content">{dept.count}</span>
                    <span className="text-xs text-base-content/60">
                      ({dept.percentage.toFixed(1)}%)
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Performance Indicators */}
          <div className="pt-4 border-t border-base-300">
            <h4 className="font-medium text-base-content/80 mb-3">Performance Indicators</h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-success/5 rounded-lg">
                <div className="text-lg font-bold text-success">
                  {stats.totalExams}
                </div>
                <div className="text-xs text-base-content/60">Exams Created</div>
              </div>
              
              <div className="text-center p-3 bg-warning/5 rounded-lg">
                <div className="text-lg font-bold text-warning">
                  {stats.totalSections}
                </div>
                <div className="text-xs text-base-content/60">Active Sections</div>
              </div>
            </div>
          </div>

          {/* Efficiency Metrics */}
          <div className="pt-4 border-t border-base-300">
            <h4 className="font-medium text-base-content/80 mb-3">Efficiency Metrics</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-base-content/70">Sections per Class</span>
                <span className="text-sm font-medium text-base-content">
                  {stats.totalClasses > 0 ? (stats.totalSections / stats.totalClasses).toFixed(1) : "0"}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-base-content/70">Subjects Coverage</span>
                <span className="text-sm font-medium text-base-content">
                  {stats.totalSubjects > 0 && stats.totalSections > 0 ? 
                    `${Math.round((stats.totalAssignments / (stats.totalSubjects * stats.totalSections)) * 100)}%` : 
                    "0%"}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-base-content/70">Exam Frequency</span>
                <span className="text-sm font-medium text-base-content">
                  {stats.totalClasses > 0 ? (stats.totalExams / stats.totalClasses).toFixed(1) : "0"} per class
                </span>
              </div>
            </div>
          </div>

          {/* Growth Indicator */}
          <div className="pt-4 border-t border-base-300">
            <div className="flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg">
              <TrendingUpIcon className="w-5 h-5 text-success" />
              <span className="text-sm font-medium text-base-content">
                System Health: <span className="text-success">Excellent</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
