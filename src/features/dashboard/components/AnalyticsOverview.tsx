import { useMemo } from "react";
import { motion } from "framer-motion";
import { 
  ChartPieIcon, 
  AcademicCapIcon, 
  UserGroupIcon,
  BookOpenIcon,
  TrophyIcon,
  CalendarIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import type { StudentEnrollment } from "@/features/students/types/students.type";
import type { Staff } from "@/features/staff/types/staff.type";
import type { Subject } from "@/features/subjects/types/subjects.type";
import type { Class } from "@/features/classes/types/class.type";
import type { Exam } from "@/features/exams/create-exams/types/exam.type";

interface AnalyticsOverviewProps {
  enrollments: StudentEnrollment[];
  staff: Staff[];
  subjects: Subject[];
  classes: Class[];
  exams: Exam[];
  isLoading?: boolean;
}

interface PieChartData {
  label: string;
  value: number;
  percentage: number;
  color: string;
}

export const AnalyticsOverview = ({ 
  enrollments, 
  staff, 
  subjects, 
  classes, 
  exams, 
  isLoading 
}: AnalyticsOverviewProps) => {
  
  const analyticsData = useMemo(() => {
    // Student Status Distribution
    const studentStatusData: PieChartData[] = [
      {
        label: 'Active',
        value: enrollments.filter(e => e.status === 'ACTIVE').length,
        percentage: 0,
        color: 'hsl(var(--su))', // success
      },
      {
        label: 'Graduated',
        value: enrollments.filter(e => e.status === 'GRADUATED').length,
        percentage: 0,
        color: 'hsl(var(--p))', // primary
      },
      {
        label: 'Withdrawn',
        value: enrollments.filter(e => e.status === 'WITHDRAWN').length,
        percentage: 0,
        color: 'hsl(var(--wa))', // warning
      },
      {
        label: 'Other',
        value: enrollments.filter(e => !['ACTIVE', 'GRADUATED', 'WITHDRAWN'].includes(e.status)).length,
        percentage: 0,
        color: 'hsl(var(--er))', // error
      },
    ];

    // Calculate percentages for student status
    const totalStudents = enrollments.length;
    studentStatusData.forEach(item => {
      item.percentage = totalStudents > 0 ? (item.value / totalStudents) * 100 : 0;
    });

    // Staff Department Distribution
    const staffDeptData: PieChartData[] = [
      {
        label: 'Academic',
        value: staff.filter(s => s.department === 'ACADEMIC').length,
        percentage: 0,
        color: 'hsl(var(--p))', // primary
      },
      {
        label: 'Administration',
        value: staff.filter(s => s.department === 'ADMINISTRATION').length,
        percentage: 0,
        color: 'hsl(var(--s))', // secondary
      },
      {
        label: 'Support',
        value: staff.filter(s => s.department === 'SUPPORT').length,
        percentage: 0,
        color: 'hsl(var(--a))', // accent
      },
    ];

    // Calculate percentages for staff departments
    const totalStaff = staff.length;
    staffDeptData.forEach(item => {
      item.percentage = totalStaff > 0 ? (item.value / totalStaff) * 100 : 0;
    });

    // Enrollment Type Distribution
    const enrollmentTypeData: PieChartData[] = [
      {
        label: 'New Admission',
        value: enrollments.filter(e => e.type === 'ADMISSION').length,
        percentage: 0,
        color: 'hsl(var(--su))', // success
      },
      {
        label: 'Transfer In',
        value: enrollments.filter(e => e.type === 'TRANSFER_IN').length,
        percentage: 0,
        color: 'hsl(var(--in))', // info
      },
      {
        label: 'Promotion',
        value: enrollments.filter(e => e.type === 'PROMOTION').length,
        percentage: 0,
        color: 'hsl(var(--wa))', // warning
      },
      {
        label: 'Repeating',
        value: enrollments.filter(e => e.type === 'REPEATING').length,
        percentage: 0,
        color: 'hsl(var(--er))', // error
      },
    ];

    // Calculate percentages for enrollment types
    enrollmentTypeData.forEach(item => {
      item.percentage = totalStudents > 0 ? (item.value / totalStudents) * 100 : 0;
    });

    return {
      studentStatusData: studentStatusData.filter(item => item.value > 0),
      staffDeptData: staffDeptData.filter(item => item.value > 0),
      enrollmentTypeData: enrollmentTypeData.filter(item => item.value > 0),
      totalStudents,
      totalStaff,
      totalSubjects: subjects.length,
      totalClasses: classes.length,
      totalExams: exams.length,
    };
  }, [enrollments, staff, subjects, classes, exams]);

  const createPieChart = (data: PieChartData[], size: number = 120) => {
    const radius = size / 2 - 10;
    const centerX = size / 2;
    const centerY = size / 2;
    
    let cumulativePercentage = 0;
    
    return (
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="transparent"
          stroke="hsl(var(--b3))"
          strokeWidth="8"
        />
        {data.map((item, index) => {
          const strokeDasharray = `${item.percentage} ${100 - item.percentage}`;
          const strokeDashoffset = -cumulativePercentage;
          cumulativePercentage += item.percentage;
          
          return (
            <motion.circle
              key={item.label}
              cx={centerX}
              cy={centerY}
              r={radius}
              fill="transparent"
              stroke={item.color}
              strokeWidth="8"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              pathLength="100"
              initial={{ strokeDasharray: "0 100" }}
              animate={{ strokeDasharray, strokeDashoffset }}
              transition={{ duration: 1, delay: index * 0.2 }}
            />
          );
        })}
      </svg>
    );
  };

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="skeleton h-64 w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-xl font-semibold mb-6 flex items-center">
          <ChartPieIcon className="w-6 h-6 mr-2 text-primary" />
          Analytics Overview
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Student Status Distribution */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-center mb-4">
              <AcademicCapIcon className="w-5 h-5 mr-2 text-primary" />
              <h4 className="font-semibold text-base-content">Student Status</h4>
            </div>
            
            <div className="relative flex justify-center mb-4">
              {createPieChart(analyticsData.studentStatusData)}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-bold text-base-content">
                    {analyticsData.totalStudents}
                  </div>
                  <div className="text-xs text-base-content/60">Total</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              {analyticsData.studentStatusData.map((item, index) => (
                <motion.div
                  key={item.label}
                  className="flex items-center justify-between text-sm"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-base-content/70">{item.label}</span>
                  </div>
                  <div className="font-medium text-base-content">
                    {item.value} ({item.percentage.toFixed(1)}%)
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Staff Department Distribution */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center justify-center mb-4">
              <UserGroupIcon className="w-5 h-5 mr-2 text-secondary" />
              <h4 className="font-semibold text-base-content">Staff Distribution</h4>
            </div>
            
            <div className="relative flex justify-center mb-4">
              {createPieChart(analyticsData.staffDeptData)}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-bold text-base-content">
                    {analyticsData.totalStaff}
                  </div>
                  <div className="text-xs text-base-content/60">Total</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              {analyticsData.staffDeptData.map((item, index) => (
                <motion.div
                  key={item.label}
                  className="flex items-center justify-between text-sm"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                >
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-base-content/70">{item.label}</span>
                  </div>
                  <div className="font-medium text-base-content">
                    {item.value} ({item.percentage.toFixed(1)}%)
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Enrollment Type Distribution */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="flex items-center justify-center mb-4">
              <BookOpenIcon className="w-5 h-5 mr-2 text-accent" />
              <h4 className="font-semibold text-base-content">Enrollment Types</h4>
            </div>
            
            <div className="relative flex justify-center mb-4">
              {createPieChart(analyticsData.enrollmentTypeData)}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-bold text-base-content">
                    {analyticsData.totalStudents}
                  </div>
                  <div className="text-xs text-base-content/60">Total</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              {analyticsData.enrollmentTypeData.map((item, index) => (
                <motion.div
                  key={item.label}
                  className="flex items-center justify-between text-sm"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
                >
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-base-content/70">{item.label}</span>
                  </div>
                  <div className="font-medium text-base-content">
                    {item.value} ({item.percentage.toFixed(1)}%)
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Additional Stats Row */}
        <div className="mt-8 pt-6 border-t border-base-300">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <motion.div
              className="text-center p-4 bg-primary/5 rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <TrophyIcon className="w-8 h-8 mx-auto text-primary mb-2" />
              <div className="text-lg font-bold text-primary">
                {((analyticsData.studentStatusData.find(s => s.label === 'Active')?.percentage ?? 0)).toFixed(1)}%
              </div>
              <div className="text-xs text-base-content/60">Active Rate</div>
            </motion.div>
            
            <motion.div
              className="text-center p-4 bg-secondary/5 rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <BookOpenIcon className="w-8 h-8 mx-auto text-secondary mb-2" />
              <div className="text-lg font-bold text-secondary">
                {analyticsData.totalSubjects}
              </div>
              <div className="text-xs text-base-content/60">Subjects</div>
            </motion.div>
            
            <motion.div
              className="text-center p-4 bg-accent/5 rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <AcademicCapIcon className="w-8 h-8 mx-auto text-accent mb-2" />
              <div className="text-lg font-bold text-accent">
                {analyticsData.totalClasses}
              </div>
              <div className="text-xs text-base-content/60">Classes</div>
            </motion.div>
            
            <motion.div
              className="text-center p-4 bg-success/5 rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <CalendarIcon className="w-8 h-8 mx-auto text-success mb-2" />
              <div className="text-lg font-bold text-success">
                {analyticsData.totalExams}
              </div>
              <div className="text-xs text-base-content/60">Exams</div>
            </motion.div>
            
            <motion.div
              className="text-center p-4 bg-warning/5 rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 1.0 }}
            >
              <ClockIcon className="w-8 h-8 mx-auto text-warning mb-2" />
              <div className="text-lg font-bold text-warning">
                {analyticsData.totalStaff > 0 ? Math.round(analyticsData.totalStudents / analyticsData.totalStaff) : 0}:1
              </div>
              <div className="text-xs text-base-content/60">Student:Staff</div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
