/* eslint-disable react-refresh/only-export-components */
import { useState, useEffect, type FC, type ReactNode } from "react";
import { Link, useLocation } from "react-router";
import {
  AcademicCapIcon,
  BookOpenIcon,
  CalendarIcon,
  ChartBarIcon,
  ClockIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  DocumentTextIcon,
  HomeModernIcon,
  BuildingLibraryIcon,
  MegaphoneIcon,
  Squares2X2Icon,
  UserGroupIcon,
  UserIcon,
  UserPlusIcon,
  BellIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  IdentificationIcon,
  DocumentCheckIcon,
} from "@heroicons/react/24/outline";
import { useSidebarStore } from "./sidebar/sidebar.store";

interface SidebarItem {
  Icon: ReactNode;
  Label: string;
  Link?: string;
  SubItems?: SidebarItem[];
}

interface SidebarGroup {
  Group: string | null;
  Items: SidebarItem[];
}

export const SidebarItemsData: SidebarGroup[] = [
  {
    Group: null,
    Items: [
      {
        Icon: <Squares2X2Icon className="h-6 w-6" />,
        Label: "Dashboard",
        Link: "/",
      },
    ],
  },
  {
    Group: "ACADEMIC",
    Items: [
      {
        Icon: <AcademicCapIcon className="h-6 w-6" />,
        Label: "Subjects",
        Link: "/subjects",
      },
      {
        Icon: <ClockIcon className="h-6 w-6" />,
        Label: "Attendance",
        Link: "/student-attendance",
      },
      {
        Icon: <DocumentCheckIcon className="h-6 w-6" />,
        Label: "Exams",
        SubItems: [
          {
            Icon: <DocumentCheckIcon className="h-5 w-5" />,
            Label: "Create Exams",
            Link: "/exams",
          },
          {
            Icon: <DocumentCheckIcon className="h-5 w-5" />,
            Label: "Assign to Classes",
            Link: "/exams/assign-to-class-sections",
          },
        ],
      },
    ],
  },
  {
    Group: "ADMINISTRATOR",
    Items: [
      {
        Icon: <UserGroupIcon className="h-6 w-6" />,
        Label: "Students",
        SubItems: [
          {
            Icon: <UserPlusIcon className="h-5 w-5" />,
            Label: "Admission",
            Link: "/students/admission",
          },
          {
            Icon: <UserGroupIcon className="h-5 w-5" />,
            Label: "All Students",
            Link: "/students",
          },
          {
            Icon: <IdentificationIcon className="h-5 w-5" />,
            Label: "ID Cards",
            Link: "/students/id-cards",
          },
        ],
      },
      {
        Icon: <UserIcon className="h-6 w-6" />,
        Label: "Staff",
        Link: "/staff",
      },
      {
        Icon: <HomeModernIcon className="h-6 w-6" />,
        Label: "Class",
        Link: "/classes",
      },
    ],
  },
  {
    Group: "SETTINGS",
    Items: [
      {
        Icon: <Cog6ToothIcon className="h-6 w-6" />,
        Label: "Account Settings",
        Link: "/account-settings",
      },
      {
        Icon: <BellIcon className="h-6 w-6" />,
        Label: "Notifications",
        Link: "/notification-preferences",
      },
    ],
  },
];

const Sidebar: FC = () => {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});
  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>(
    {}
  );
  const { isOpen, close } = useSidebarStore();
  const location = useLocation();

  // Initialize with all sections closed on mobile and open on desktop
  useEffect(() => {
    const initialState: Record<string, boolean> = {};
    SidebarItemsData.forEach((group) => {
      if (group.Group) {
        initialState[group.Group] = window.innerWidth >= 768;
      }
    });
    setOpenSections(initialState);
  }, []);

  const toggleSection = (title: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  const toggleDropdown = (label: string) => {
    setOpenDropdowns((prev) => ({
      ...prev,
      [label]: !prev[label],
    }));
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="md:hidden fixed inset-0 bg-semi-transparent bg-opacity-50 z-40"
          onClick={close}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 h-screen bg-base-200 border-r border-base-300 z-40
          transition-all duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"}
          shadow-[2px_0_10px_rgba(0,0,0,0.05)]
          sidebar-scrollbar
          overflow-y-auto
          pt-16 md:pt-[72px] /* Add padding for header */
        `}
      >
        <div className="flex justify-between items-center p-4 md:hidden absolute top-0 left-0 right-0 bg-base-200 border-b border-base-300">
          <span className="font-bold text-primary">Menu</span>
          <button
            onClick={close}
            className="p-1 rounded-full hover:bg-base-300 transition-colors"
            aria-label="Close sidebar"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Logo for sidebar - small version */}
        <div className="px-4 py-3 border-b border-base-300">
          <Link to="/" className="flex items-center gap-2">
            <Squares2X2Icon className="h-6 w-6 text-primary" />
            <span className="font-bold text-lg">E-Bridge</span>
          </Link>
        </div>

        <div className="p-3 sidebar-content pb-20">
          <ul className="space-y-2">
            {SidebarItemsData.map((group, groupIndex) => {
              const groupKey = group.Group ?? `group-${groupIndex}`;

              return (
                <li key={groupKey} className="mb-3">
                  {group.Group ? (
                    <div className="mb-1">
                      <button
                        onClick={() => {
                          toggleSection(group.Group!);
                        }}
                        className="flex items-center justify-between w-full px-3 py-2 text-xs text-base-content/80 uppercase tracking-wide font-semibold bg-base-300/50 rounded-md hover:bg-base-300 transition-colors"
                      >
                        <span>{group.Group}</span>
                        {openSections[group.Group] ? (
                          <ChevronDownIcon className="w-4 h-4 text-primary" />
                        ) : (
                          <ChevronRightIcon className="w-4 h-4 text-primary" />
                        )}
                      </button>
                      <div
                        className={`mt-1 overflow-hidden transition-all duration-300 ease-in-out ${
                          openSections[group.Group]
                            ? "max-h-[1000px] opacity-100"
                            : "max-h-0 opacity-0"
                        }`}
                      >
                        <ul className="pl-2 space-y-1">
                          {group.Items.map((item, itemIndex) => {
                            if (item.SubItems) {
                              // Dropdown item
                              const isDropdownOpen = openDropdowns[item.Label];
                              const hasActiveSubItem = item.SubItems.some(
                                (subItem) => location.pathname === subItem.Link
                              );

                              return (
                                <li key={`${groupKey}-${itemIndex}`}>
                                  <button
                                    onClick={() => {
                                      toggleDropdown(item.Label);
                                    }}
                                    className={`flex items-center justify-between w-full px-3 py-2.5 rounded-md transition-all hover:bg-base-300
                                      ${
                                        hasActiveSubItem
                                          ? "bg-primary/10 text-primary font-medium border-l-4 border-primary"
                                          : "border-l-4 border-transparent"
                                      }`}
                                  >
                                    <div className="flex items-center">
                                      <div
                                        className={`mr-3 ${hasActiveSubItem ? "text-primary" : ""}`}
                                      >
                                        {item.Icon}
                                      </div>
                                      <span>{item.Label}</span>
                                    </div>
                                    {isDropdownOpen ? (
                                      <ChevronDownIcon className="w-4 h-4 text-primary" />
                                    ) : (
                                      <ChevronRightIcon className="w-4 h-4 text-primary" />
                                    )}
                                  </button>

                                  <div
                                    className={`mt-1 overflow-hidden transition-all duration-300 ease-in-out ${
                                      isDropdownOpen
                                        ? "max-h-[500px] opacity-100"
                                        : "max-h-0 opacity-0"
                                    }`}
                                  >
                                    <ul className="pl-6 space-y-1">
                                      {item.SubItems.map(
                                        (subItem, subIndex) => {
                                          const isActive =
                                            location.pathname === subItem.Link;
                                          return (
                                            <li
                                              key={`${groupKey}-${itemIndex}-${subIndex}`}
                                            >
                                              <Link
                                                to={subItem.Link!}
                                                className={`flex items-center px-3 py-2 rounded-md transition-all hover:bg-base-300 text-sm
                                                ${
                                                  isActive
                                                    ? "bg-primary/10 text-primary font-medium border-l-2 border-primary"
                                                    : "border-l-2 border-transparent"
                                                }`}
                                                onClick={() => {
                                                  if (window.innerWidth < 768) {
                                                    close();
                                                  }
                                                }}
                                              >
                                                <div
                                                  className={`mr-3 ${isActive ? "text-primary" : ""}`}
                                                >
                                                  {subItem.Icon}
                                                </div>
                                                <span>{subItem.Label}</span>
                                              </Link>
                                            </li>
                                          );
                                        }
                                      )}
                                    </ul>
                                  </div>
                                </li>
                              );
                            } else {
                              // Regular item
                              const isActive = location.pathname === item.Link;
                              return (
                                <li key={`${groupKey}-${itemIndex}`}>
                                  <Link
                                    to={item.Link!}
                                    className={`flex items-center px-3 py-2.5 rounded-md transition-all hover:bg-base-300
                                      ${
                                        isActive
                                          ? "bg-primary/10 text-primary font-medium border-l-4 border-primary"
                                          : "border-l-4 border-transparent"
                                      }`}
                                    onClick={() => {
                                      if (window.innerWidth < 768) {
                                        close();
                                      }
                                    }}
                                  >
                                    <div
                                      className={`mr-3 ${isActive ? "text-primary" : ""}`}
                                    >
                                      {item.Icon}
                                    </div>
                                    <span>{item.Label}</span>
                                  </Link>
                                </li>
                              );
                            }
                          })}
                        </ul>
                      </div>
                    </div>
                  ) : (
                    <div className="mb-3">
                      {group.Items.map((item, itemIndex) => {
                        if (item.SubItems) {
                          // Dropdown item (for non-grouped items)
                          const isDropdownOpen = openDropdowns[item.Label];
                          const hasActiveSubItem = item.SubItems.some(
                            (subItem) => location.pathname === subItem.Link
                          );

                          return (
                            <div
                              key={`${groupKey}-${itemIndex}`}
                              className="mb-2"
                            >
                              <button
                                onClick={() => {
                                  toggleDropdown(item.Label);
                                }}
                                className={`flex items-center justify-between w-full px-3 py-2.5 rounded-md transition-all hover:bg-base-300
                                  ${
                                    hasActiveSubItem
                                      ? "bg-primary/10 text-primary font-medium border-l-4 border-primary"
                                      : "border-l-4 border-transparent"
                                  }`}
                              >
                                <div className="flex items-center">
                                  <div
                                    className={`mr-3 ${hasActiveSubItem ? "text-primary" : ""}`}
                                  >
                                    {item.Icon}
                                  </div>
                                  <span>{item.Label}</span>
                                </div>
                                {isDropdownOpen ? (
                                  <ChevronDownIcon className="w-4 h-4 text-primary" />
                                ) : (
                                  <ChevronRightIcon className="w-4 h-4 text-primary" />
                                )}
                              </button>

                              <div
                                className={`mt-1 overflow-hidden transition-all duration-300 ease-in-out ${
                                  isDropdownOpen
                                    ? "max-h-[500px] opacity-100"
                                    : "max-h-0 opacity-0"
                                }`}
                              >
                                <ul className="pl-6 space-y-1">
                                  {item.SubItems.map((subItem, subIndex) => {
                                    const isActive =
                                      location.pathname === subItem.Link;
                                    return (
                                      <li
                                        key={`${groupKey}-${itemIndex}-${subIndex}`}
                                      >
                                        <Link
                                          to={subItem.Link!}
                                          className={`flex items-center px-3 py-2 rounded-md transition-all hover:bg-base-300 text-sm
                                            ${
                                              isActive
                                                ? "bg-primary/10 text-primary font-medium border-l-2 border-primary"
                                                : "border-l-2 border-transparent"
                                            }`}
                                          onClick={() => {
                                            if (window.innerWidth < 768) {
                                              close();
                                            }
                                          }}
                                        >
                                          <div
                                            className={`mr-3 ${isActive ? "text-primary" : ""}`}
                                          >
                                            {subItem.Icon}
                                          </div>
                                          <span>{subItem.Label}</span>
                                        </Link>
                                      </li>
                                    );
                                  })}
                                </ul>
                              </div>
                            </div>
                          );
                        } else {
                          // Regular item
                          const isActive = location.pathname === item.Link;
                          return (
                            <Link
                              key={`${groupKey}-${itemIndex}`}
                              to={item.Link!}
                              className={`flex items-center px-3 py-2.5 rounded-md transition-all hover:bg-base-300
                                ${
                                  isActive
                                    ? "bg-primary/10 text-primary font-medium border-l-4 border-primary"
                                    : "border-l-4 border-transparent"
                                }`}
                              onClick={() => {
                                if (window.innerWidth < 768) {
                                  close();
                                }
                              }}
                            >
                              <div
                                className={`mr-3 ${isActive ? "text-primary" : ""}`}
                              >
                                {item.Icon}
                              </div>
                              <span>{item.Label}</span>
                            </Link>
                          );
                        }
                      })}
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
