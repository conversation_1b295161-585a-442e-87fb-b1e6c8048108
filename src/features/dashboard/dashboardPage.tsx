import { useMemo } from "react";
import { motion } from "framer-motion";
import {
  AcademicCapIcon,
  UserGroupIcon,
  BookOpenIcon,
  ClockIcon,
  ChartBarIcon,
  TrophyIcon,
  DocumentCheckIcon,
  UserIcon,
  BuildingLibraryIcon,
} from "@heroicons/react/24/outline";

import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { RequiresResource } from "@/common/components/RequiresResourceCard";

// Import existing hooks
import { useEnrollments } from "@/features/students/hooks/useEnrollements";
import { useStaff } from "@/features/staff/services/staff-query";
import { useSubjects } from "@/features/subjects/service/subjects-query";
import { useClasses } from "@/features/classes/service/class-query";
import { useExams } from "@/features/exams/create-exams/services/exam-query";
import { useAssignedSubjects } from "@/features/assign-subjects/service/assign-subjects-query";

// Import reusable components
import { ExamStatsCard } from "@/features/exams/exam-board/components/ExamStatsCard";

// Import new dashboard components
import {
  StudentEnrollmentChart,
  StaffDistributionChart,
  ClassPerformanceOverview,
  RecentActivities,
  QuickStats,
  AttendanceOverview,
  AnalyticsOverview,
} from "./components";

export const DashboardPage = () => {
  const { activeAcademicSession } = useAcademicSessionStore();
  const { selectedBranch } = useBranchStore();

  // Fetch all data
  const { data: enrollmentsData, isLoading: enrollmentsLoading } =
    useEnrollments(activeAcademicSession?.id);
  const { data: staffData, isLoading: staffLoading } = useStaff(
    selectedBranch?.id
  );
  const { data: subjectsData, isLoading: subjectsLoading } = useSubjects(
    activeAcademicSession?.id
  );
  const { data: classesData, isLoading: classesLoading } = useClasses(
    activeAcademicSession?.id
  );
  const { data: examsData, isLoading: examsLoading } = useExams(
    activeAcademicSession?.id
  );
  const { data: assignedSubjectsData } = useAssignedSubjects(
    activeAcademicSession?.id
  );

  // Calculate statistics
  const stats = useMemo(() => {
    const totalStudents =
      enrollmentsData?.items.filter((e) => e.status === "ACTIVE").length ?? 0;
    const totalStaff = staffData?.items.length ?? 0;
    const totalSubjects = subjectsData?.items.length ?? 0;
    const totalClasses = classesData?.items.length ?? 0;
    const totalExams = examsData?.items.length ?? 0;
    const totalAssignments = assignedSubjectsData?.items.length ?? 0;

    // Calculate class sections
    const totalSections =
      classesData?.items.reduce((sum, cls) => sum + cls.sections.length, 0) ??
      0;

    // Calculate staff by department
    const staffByDepartment =
      staffData?.items.reduce(
        (acc, staff) => {
          acc[staff.department] = (acc[staff.department] ?? 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) ?? {};

    return {
      totalStudents,
      totalStaff,
      totalSubjects,
      totalClasses,
      totalSections,
      totalExams,
      totalAssignments,
      staffByDepartment,
    };
  }, [
    enrollmentsData,
    staffData,
    subjectsData,
    classesData,
    examsData,
    assignedSubjectsData,
  ]);

  const isLoading =
    enrollmentsLoading ||
    staffLoading ||
    subjectsLoading ||
    classesLoading ||
    examsLoading;

  if (!activeAcademicSession) {
    return (
      <RequiresResource resourceName="Academic Session" createUrl="/sessions" />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-4xl font-bold text-base-content">
            Welcome to E-Bridge Dashboard
          </h1>
          <p className="text-base-content/70 mt-2 text-lg">
            Comprehensive overview of your school management system
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-base-content/60">Academic Session</div>
          <div className="font-semibold text-lg text-primary">
            {activeAcademicSession.name}
          </div>
          <div className="text-sm text-base-content/60">
            {selectedBranch?.name}
          </div>
        </div>
      </motion.div>

      {/* Quick Stats Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <ExamStatsCard
          title="Total Students"
          value={stats.totalStudents}
          subtitle="Active enrollments"
          icon={<AcademicCapIcon className="w-6 h-6" />}
          color="primary"
        />
        <ExamStatsCard
          title="Staff Members"
          value={stats.totalStaff}
          subtitle="All departments"
          icon={<UserGroupIcon className="w-6 h-6" />}
          color="secondary"
        />
        <ExamStatsCard
          title="Subjects"
          value={stats.totalSubjects}
          subtitle="This session"
          icon={<BookOpenIcon className="w-6 h-6" />}
          color="accent"
        />
        <ExamStatsCard
          title="Classes"
          value={`${stats.totalClasses}/${stats.totalSections}`}
          subtitle="Classes/Sections"
          icon={<BuildingLibraryIcon className="w-6 h-6" />}
          color="success"
        />
      </motion.div>

      {/* Secondary Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <ExamStatsCard
          title="Exams Created"
          value={stats.totalExams}
          subtitle="This session"
          icon={<DocumentCheckIcon className="w-6 h-6" />}
          color="warning"
        />
        <ExamStatsCard
          title="Subject Assignments"
          value={stats.totalAssignments}
          subtitle="Teacher assignments"
          icon={<UserIcon className="w-6 h-6" />}
          color="error"
        />
        <ExamStatsCard
          title="Academic Progress"
          value="85%"
          subtitle="Session completion"
          icon={<TrophyIcon className="w-6 h-6" />}
          color="success"
        />
      </motion.div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - 2/3 width */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="lg:col-span-2 space-y-6"
        >
          {/* Student Enrollment Chart */}
          <StudentEnrollmentChart
            enrollments={enrollmentsData?.items ?? []}
            isLoading={isLoading}
          />

          {/* Class Performance Overview */}
          <ClassPerformanceOverview
            classes={classesData?.items ?? []}
            enrollments={enrollmentsData?.items ?? []}
            isLoading={isLoading}
          />

          {/* Analytics Overview with Pie Charts */}
          <AnalyticsOverview
            enrollments={enrollmentsData?.items ?? []}
            staff={staffData?.items ?? []}
            subjects={subjectsData?.items ?? []}
            classes={classesData?.items ?? []}
            exams={examsData?.items ?? []}
            isLoading={isLoading}
          />
        </motion.div>

        {/* Right Column - 1/3 width */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="space-y-6"
        >
          {/* Staff Distribution */}
          <StaffDistributionChart
            staff={staffData?.items ?? []}
            isLoading={isLoading}
          />

          {/* Quick Stats */}
          <QuickStats stats={stats} isLoading={isLoading} />

          {/* Recent Activities */}
          <RecentActivities
            enrollments={enrollmentsData?.items ?? []}
            exams={examsData?.items ?? []}
            isLoading={isLoading}
          />
        </motion.div>
      </div>

      {/* Bottom Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <AttendanceOverview
          classes={classesData?.items ?? []}
          isLoading={isLoading}
        />
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="card bg-gradient-to-r from-primary/5 to-secondary/5 border border-primary/20"
      >
        <div className="card-body p-6">
          <h3 className="card-title text-xl font-semibold mb-4 flex items-center">
            <ChartBarIcon className="w-6 h-6 mr-2 text-primary" />
            Quick Actions
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <a
              href="/students/admission"
              className="btn btn-outline btn-primary"
            >
              <AcademicCapIcon className="w-4 h-4 mr-2" />
              Add Student
            </a>
            <a href="/staff" className="btn btn-outline btn-secondary">
              <UserGroupIcon className="w-4 h-4 mr-2" />
              Add Staff
            </a>
            <a href="/subjects" className="btn btn-outline btn-accent">
              <BookOpenIcon className="w-4 h-4 mr-2" />
              Add Subject
            </a>
            <a href="/classes" className="btn btn-outline btn-success">
              <BuildingLibraryIcon className="w-4 h-4 mr-2" />
              Add Class
            </a>
            <a href="/exams" className="btn btn-outline btn-warning">
              <DocumentCheckIcon className="w-4 h-4 mr-2" />
              Create Exam
            </a>
            <a href="/student-attendance" className="btn btn-outline btn-info">
              <ClockIcon className="w-4 h-4 mr-2" />
              Take Attendance
            </a>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
