import { useState, useRef, useEffect, useMemo } from "react";
import { useLocation, useNavigate } from "react-router";
import { motion } from "framer-motion";
import html2canvas from "html2canvas-pro";
import QRCode from "qrcode";
import {
  IdentificationIcon,
  ArrowDownTrayIcon,
  ArrowLeftIcon,
} from "@heroicons/react/24/outline";

import { Button } from "@/common/components/ui/Button";
import { useStudentCardTemplate } from "../services/student-id-cards-query";
import type { SelectedStudent } from "../types/student-id-cards.type";
import { StudentCardPreview } from "../components/StudentCard";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";

export const CardGenerationPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  const [qrCodes, setQrCodes] = useState<Record<string, string>>({});
  const [downloadFormat, setDownloadFormat] = useState<"png" | "jpeg">("png");
  const [isGenerating, setIsGenerating] = useState(false);

  const selectedStudents: SelectedStudent[] = useMemo(() => {
    return (
      (location.state as { selectedStudents: SelectedStudent[] } | undefined)
        ?.selectedStudents ?? []
    );
  }, [location.state]);

  const { selectedBranch } = useBranchStore();
  const { data: cardTemplate } = useStudentCardTemplate(selectedBranch?.id);
  const myRef = useRef<HTMLDivElement>(null);
  // Generate QR codes for all students
  useEffect(() => {
    const generateQRCodes = async () => {
      const qrCodePromises = selectedStudents.map(async (student) => {
        try {
          const qrDataUrl = await QRCode.toDataURL(student.enrollmentId, {
            width: 120,
            margin: 1,
            color: {
              dark: "#000000",
              light: "#FFFFFF",
            },
          });
          return { enrollmentId: student.enrollmentId, qrCode: qrDataUrl };
        } catch (error) {
          console.error(`Error generating QR code for ${student.name}:`, error);
          return { enrollmentId: student.enrollmentId, qrCode: "" };
        }
      });

      const results = await Promise.all(qrCodePromises);
      const qrCodeMap = results.reduce<Record<string, string>>(
        (acc, { enrollmentId, qrCode }) => {
          acc[enrollmentId] = qrCode;
          return acc;
        },
        {}
      );

      setQrCodes(qrCodeMap);
    };

    if (selectedStudents.length > 0) {
      void generateQRCodes();
    }
  }, [selectedStudents]);

  const downloadCard = async (index: number, student: SelectedStudent) => {
    const cardElement = cardRefs.current[index];

    if (!cardElement) return;

    try {
      const canvas = await html2canvas(cardElement, {
        scale: 2,
        backgroundColor: "#ffffff",
        useCORS: true,
        allowTaint: true,
      });

      const link = document.createElement("a");
      link.download = `${student.name.replace(/\s+/g, "_")}_${student.rollNumber}_${student.className}_${student.sectionName}_Card.${downloadFormat}`;

      if (downloadFormat === "png") {
        link.href = canvas.toDataURL("image/png");
      } else {
        link.href = canvas.toDataURL("image/jpeg", 0.9);
      }

      link.click();
    } catch (error) {
      console.error("Error downloading card:", error);
      alert("Failed to download card. Please try again.");
    }
  };

  const downloadAllCards = async () => {
    setIsGenerating(true);

    try {
      const downloadPromises = selectedStudents.map((student, index) =>
        downloadCard(index, student)
      );

      await Promise.all(downloadPromises);

      alert("All cards downloaded successfully!");
    } catch (error) {
      console.error("Error downloading cards:", error);
      alert("Some cards failed to download. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    console.log(myRef.current, cardRefs.current);
  }, []);

  if (selectedStudents.length === 0) {
    return (
      <div className="min-h-screen bg-base-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-base-content mb-2">
            No Students Selected
          </h2>
          <p className="text-base-content/70 mb-4">
            Please go back and select students to generate ID cards.
          </p>
          <Button onClick={() => navigate("/students/id-cards")}>
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-100 p-6" ref={myRef}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Button
              onClick={() => navigate("/students/id-cards")}
              shape="neutral"
              outline
              className="flex items-center space-x-2"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <div className="flex items-center space-x-3">
              <IdentificationIcon className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold text-base-content">
                  Generate ID Cards
                </h1>
                <p className="text-base-content/70">
                  {selectedStudents.length} student
                  {selectedStudents.length > 1 ? "s" : ""} selected
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <select
              value={downloadFormat}
              onChange={(e) => {
                setDownloadFormat(e.target.value as "png" | "jpeg");
              }}
              className="select select-bordered select-sm"
            >
              <option value="png">PNG</option>
              <option value="jpeg">JPEG</option>
            </select>

            <Button
              onClick={downloadAllCards}
              disabled={isGenerating || Object.keys(qrCodes).length === 0}
              className="flex items-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <ArrowDownTrayIcon className="w-4 h-4" />
                  <span>Download All</span>
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="grid gap-4 [grid-template-columns:repeat(auto-fit,minmax(360px,1fr))]">
          {selectedStudents.map((student, index) => (
            <motion.div
              key={student.enrollmentId}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-lg p-4 min-w-[400px]"
            >
              <StudentCardPreview
                headerImage={cardTemplate?.headerImage}
                studentClass={student.className}
                studentSection={student.sectionName}
                studentRollNumber={student.rollNumber}
                studentName={student.name}
                backgroundImage={cardTemplate?.backgroundImage ?? ""}
                title={cardTemplate?.title ?? "Title"}
                watermark={cardTemplate?.watermark ?? "Watermark"}
                qrCode={qrCodes[student.enrollmentId] ?? ""}
                studentPhoto={student.studentImage}
                setRef={(el) => {
                  cardRefs.current[index] = el;
                }}
              />

              {/* Individual Download Button */}
              <div className="mt-4 text-center">
                <Button
                  onClick={() => downloadCard(index, student)}
                  disabled={!qrCodes[student.enrollmentId]}
                  size="sm"
                  outline
                  className="flex items-center space-x-2 mx-auto"
                >
                  <ArrowDownTrayIcon className="w-4 h-4" />
                  <span>Download</span>
                </Button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};
