import { useState, useEffect } from "react";
import { useNavigate } from "react-router";
import { motion } from "framer-motion";
import {
  MagnifyingGlassIcon,
  IdentificationIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";

import { Button } from "@/common/components/ui/Button";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { useEnrollments } from "@/features/students/hooks/useEnrollements";
import { useClasses } from "@/features/classes/service/class-query";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import type { StudentEnrollment } from "@/features/students/types/students.type";
import type {
  StudentIdCardFilters,
  SelectedStudent,
} from "../types/student-id-cards.type";

export const StudentIdCardsListPage = () => {
  const navigate = useNavigate();
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<StudentIdCardFilters>({});
  const [filteredStudents, setFilteredStudents] = useState<StudentEnrollment[]>(
    []
  );

  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData } = useClasses(activeAcademicSession?.id);
  const { data: enrollmentsData, isLoading } = useEnrollments(
    activeAcademicSession?.id,
    {
      classId: filters.classId,
      sectionId: filters.sectionId,
      status: filters.status,
      offset: 0,
      limit: 100, // Get more records for ID card generation
    }
  );

  // Filter students based on search term
  useEffect(() => {
    if (!enrollmentsData?.items) {
      setFilteredStudents([]);
      return;
    }

    let filtered = enrollmentsData.items;

    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (enrollment) =>
          enrollment.student.name.toLowerCase().includes(searchLower) ||
          enrollment.student.rollNumber.toString().includes(searchLower)
      );
    }

    setFilteredStudents(filtered);
  }, [enrollmentsData?.items, searchTerm]);

  const handleClassChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const classId = e.target.value || undefined;
    setFilters((prev) => ({
      ...prev,
      classId,
      sectionId: undefined, // Reset section when class changes
    }));
  };

  const handleSectionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const sectionId = e.target.value || undefined;
    setFilters((prev) => ({ ...prev, sectionId }));
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const status = e.target.value as StudentEnrollment["status"] | undefined;
    setFilters((prev) => ({ ...prev, status: status ?? undefined }));
  };

  const handleSelectAll = () => {
    if (selectedStudents.length === filteredStudents.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(filteredStudents.map((enrollment) => enrollment.id));
    }
  };

  const handleStudentSelect = (enrollmentId: string) => {
    setSelectedStudents((prev) =>
      prev.includes(enrollmentId)
        ? prev.filter((id) => id !== enrollmentId)
        : [...prev, enrollmentId]
    );
  };

  const handleGenerateCards = async () => {
    if (selectedStudents.length === 0) return;

    const selectedStudentData: SelectedStudent[] = filteredStudents
      .filter((enrollment) => selectedStudents.includes(enrollment.id))
      .map((enrollment) => ({
        enrollmentId: enrollment.id,
        studentId: enrollment.student.id,
        name: enrollment.student.name,
        rollNumber: enrollment.student.rollNumber,
        className: enrollment.classSection.class.name,
        sectionName: enrollment.classSection.name,
        studentImage: enrollment.student.photo,
      }));

    // Navigate to card generation page with selected students
    await navigate("/students/id-cards/generate", {
      state: { selectedStudents: selectedStudentData },
    });
  };

  const getClassOptions = () => {
    return [
      ...(classesData?.items.map((cls) => ({
        value: cls.id,
        label: cls.name,
      })) ?? []),
    ];
  };

  const getSectionOptions = () => {
    if (!filters.classId) return [];

    const selectedClass = classesData?.items.find(
      (cls) => cls.id === filters.classId
    );
    return [
      ...(selectedClass?.sections.map((section) => ({
        value: section.id,
        label: section.name,
      })) ?? []),
    ];
  };

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "ACTIVE", label: "Active" },
    { value: "EXPELLED", label: "Expelled" },
    { value: "GRADUATED", label: "Graduated" },
    { value: "DECEASED", label: "Deceased" },
    { value: "COMPLETED", label: "Completed" },
    { value: "WITHDRAWN", label: "Withdrawn" },
  ];

  return (
    <div className="min-h-screen bg-base-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <IdentificationIcon className="w-8 h-8 text-primary" />
            <div>
              <h1 className="text-2xl font-bold text-base-content">
                Student ID Cards
              </h1>
              <p className="text-base-content/70">
                Select students to generate ID cards
              </p>
            </div>
          </div>

          <Button
            onClick={() => navigate("/students/id-cards/setup")}
            outline
            className="flex items-center space-x-2"
          >
            <span>Card Settings</span>
          </Button>
        </div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-base-200 rounded-lg p-4 mb-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                }}
                className="input input-bordered w-full pr-10"
                placeholder="Search by name or roll number..."
              />
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            <SelectField
              name="classId"
              label=""
              value={filters.classId ?? ""}
              options={getClassOptions()}
              onChange={handleClassChange}
            />

            <SelectField
              name="sectionId"
              label=""
              value={filters.sectionId ?? ""}
              options={getSectionOptions()}
              onChange={handleSectionChange}
              disabled={!filters.classId}
            />

            <SelectField
              name="status"
              label=""
              value={filters.status ?? ""}
              options={statusOptions}
              onChange={handleStatusChange}
              disabled={!filters.sectionId}
            />
          </div>
        </motion.div>

        {/* Selection Actions */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={
                  selectedStudents.length === filteredStudents.length &&
                  filteredStudents.length > 0
                }
                onChange={handleSelectAll}
                className="checkbox checkbox-primary"
              />
              <span className="text-sm font-medium">
                Select All ({filteredStudents.length})
              </span>
            </label>

            {selectedStudents.length > 0 && (
              <span className="text-sm text-primary font-medium">
                {selectedStudents.length} selected
              </span>
            )}
          </div>

          <Button
            onClick={handleGenerateCards}
            disabled={selectedStudents.length === 0}
            className="flex items-center space-x-2"
          >
            <IdentificationIcon className="w-4 h-4" />
            <span>Generate Cards ({selectedStudents.length})</span>
          </Button>
        </div>

        {/* Students Table */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-white shadow-md rounded-lg overflow-hidden"
        >
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="bg-[#0f243f] text-white">
                <tr>
                  <th className="w-[40px] text-left p-3">
                    <input
                      type="checkbox"
                      checked={
                        selectedStudents.length === filteredStudents.length &&
                        filteredStudents.length > 0
                      }
                      onChange={handleSelectAll}
                      className="checkbox checkbox-primary checkbox-sm"
                    />
                  </th>
                  <th className="w-[40px] text-left p-3">#</th>
                  <th className="p-3 text-center">Name</th>
                  <th className="p-3 text-center">Roll No</th>
                  <th className="p-3 text-center">Class</th>
                  <th className="p-3 text-center">Section</th>
                  <th className="p-3 text-center">Status</th>
                  <th className="p-3 text-center">Card Status</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={8} className="text-center py-8">
                      <div className="loading loading-spinner loading-lg text-primary mx-auto"></div>
                    </td>
                  </tr>
                ) : filteredStudents.length > 0 ? (
                  filteredStudents.map((enrollment, index) => (
                    <tr key={enrollment.id} className="hover:bg-gray-50">
                      <td className="p-3">
                        <input
                          type="checkbox"
                          checked={selectedStudents.includes(enrollment.id)}
                          onChange={() => {
                            handleStudentSelect(enrollment.id);
                          }}
                          className="checkbox checkbox-primary checkbox-sm"
                        />
                      </td>
                      <td className="p-3 text-left font-medium">{index + 1}</td>
                      <td className="py-3 px-3 text-center">
                        {enrollment.student.name}
                      </td>
                      <td className="py-3 px-3 text-center">
                        {enrollment.student.rollNumber}
                      </td>
                      <td className="py-3 px-3 text-center">
                        {enrollment.classSection.class.name}
                      </td>
                      <td className="py-3 px-3 text-center">
                        {enrollment.classSection.name}
                      </td>
                      <td className="py-3 px-3 text-center">
                        <span
                          className={`badge badge-sm ${
                            enrollment.status === "ACTIVE"
                              ? "badge-success"
                              : "badge-warning"
                          }`}
                        >
                          {enrollment.status}
                        </span>
                      </td>
                      <td className="py-3 px-3 text-center">
                        {enrollment.card ? (
                          <span className="flex items-center justify-center text-success">
                            <CheckIcon className="w-4 h-4 mr-1" />
                            Generated
                          </span>
                        ) : (
                          <span className="text-base-content/50">
                            Not Generated
                          </span>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="text-center py-8 text-gray-500">
                      No students found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
