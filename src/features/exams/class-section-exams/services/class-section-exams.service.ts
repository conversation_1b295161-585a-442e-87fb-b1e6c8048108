import { sendApiRequest } from "@/common/services/api.service";
import type { PaginatedApiResponse } from "@/common/types/global.types";
import { logger } from "@/lib/logger";
import type { ClassSectionExam } from "../types/class-section-exams.type";

export async function fetchAllClassSectionsForExam(examId: string) {
  try {
    return await sendApiRequest<PaginatedApiResponse<ClassSectionExam>>(
      `/exams/${examId}/class-sections`,
      {
        method: "GET",
        withAuthorization: true,
      }
    );
  } catch (error: unknown) {
    logger.error("Error fetching class sections for exam", error);
    throw error;
  }
}
