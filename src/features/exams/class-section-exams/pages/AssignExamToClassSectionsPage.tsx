import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";
import { ResourcePage } from "@/common/components/ResourcePage";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { zodResolver } from "@hookform/resolvers/zod";
import { type SubmitHandler, useForm } from "react-hook-form";
import { assignExamToClassSectionsSchema } from "../schema/assign-exam-to-class-sections.schema";
import { z } from "zod";
import { useClasses } from "../../../classes/service/class-query";
import { useAcademicSessionStore } from "../../../academic-session/session.store";
import { type ChangeEvent, useEffect, useState } from "react";
import type { Class } from "../../../classes/types/class.type";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import {
  useClassSectionsForExam,
  useAssignExamToClassSections,
} from "../services/class-section-exams-query";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import type { ClassSectionExam } from "../types/class-section-exams.type";
import { logger } from "@/lib/logger";
import { formatDateInLocalFormat } from "@/utils/utils";
import { useExams } from "../../create-exams/services/exam-query";
import type { Exam } from "../../create-exams/types/exam.type";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import { ASSIGN_EXAM_FORM_DEFAULT_VALUES } from "../constants/assign-exam-constants";

export const AssignExamToClassSectionsPage = () => {
  // State for pagination and UI
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [selectedClassSectionExam, setSelectedClassSectionExam] =
    useState<ClassSectionExam | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Fetch data
  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData } = useClasses(activeAcademicSession?.id);
  const { data: examsData } = useExams(activeAcademicSession?.id);
  const { data: classSectionExamsData } = useClassSectionsForExam(
    selectedExam?.id
  );

  // Mutations
  const assignExamMutation = useAssignExamToClassSections(selectedExam?.id);

  // Form setup
  type FormData = z.infer<typeof assignExamToClassSectionsSchema>;

  const {
    register,
    handleSubmit,
    reset: resetForm,
    setValue,
    watch,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(assignExamToClassSectionsSchema),
    defaultValues: ASSIGN_EXAM_FORM_DEFAULT_VALUES,
  });

  const watchedExamId = watch("examId");

  // Update selected exam when form exam changes
  useEffect(() => {
    if (watchedExamId && examsData?.items) {
      const exam = examsData.items.find((e) => e.id === watchedExamId);
      setSelectedExam(exam ?? null);
    }
  }, [watchedExamId, examsData]);

  const handleClassChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const selectedClassId = e.target.value;
    const selectedClass = classesData?.items.find(
      (c) => c.id === selectedClassId
    );
    setSelectedClass(selectedClass ?? null);
  };

  const handleExamChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const selectedExamId = e.target.value;
    const selectedExam = examsData?.items.find((e) => e.id === selectedExamId);
    setSelectedExam(selectedExam ?? null);
    setValue("examId", selectedExamId);
  };

  const onSubmit: SubmitHandler<FormData> = (data) => {
    if (!activeAcademicSession) {
      logger.error(
        "Cannot assign exam to class sections. No active academic session"
      );
      return;
    }

    if (!selectedExam) {
      logger.error("No exam selected");
      return;
    }

    assignExamMutation.mutate({
      examId: selectedExam.id,
      payload: { classSectionIds: data.classSectionIds },
    });

    resetForm(ASSIGN_EXAM_FORM_DEFAULT_VALUES);
    setSelectedClass(null);
    setSelectedExam(null);
  };

  return (
    <ResourcePage>
      <ResourceCreationForm
        isFormSubmitting={isSubmitting}
        isEditMode={false}
        onFormSubmit={handleSubmit(onSubmit)}
        resourceLabel="Exam Assignment"
        styles={{ submitButton: "justify-self-start" }}
      >
        <SelectField
          name="examId"
          onChange={handleExamChange}
          label="Exam"
          value={selectedExam?.id ?? ""}
          errorMessage={fieldErrors.examId?.message}
          defaultOptionLabel="-- Select Exam --"
          options={examsData?.items.map((exam) => ({
            value: exam.id,
            label: exam.name,
          }))}
        />

        <SelectField
          name="class"
          onChange={handleClassChange}
          label="Class"
          value={selectedClass?.id ?? ""}
          defaultOptionLabel="-- Select Class --"
          options={classesData?.items.map((classItem) => ({
            value: classItem.id,
            label: classItem.name,
          }))}
        />

        {selectedClass && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Class Sections
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
              {selectedClass.sections.map((section) => (
                <label key={section.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    value={section.id}
                    {...register("classSectionIds")}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm">
                    {section.name} (Teacher: {section.classTeacher.name})
                  </span>
                </label>
              ))}
            </div>
            {fieldErrors.classSectionIds && (
              <p className="text-red-500 text-sm mt-1">
                {fieldErrors.classSectionIds.message}
              </p>
            )}
          </div>
        )}
      </ResourceCreationForm>

      {selectedExam && (
        <ResourceListingTable
          records={classSectionExamsData?.items}
          totalRecords={classSectionExamsData?.total}
          isDataLoading={false}
          searchLabel="class section"
          searchFieldValue={(record) =>
            `${record.classSection.class.name} - ${record.classSection.name}`
          }
          pagination={paginationParams}
          onPaginationChange={setPaginationParams}
          columns={[
            { label: "Class", render: (s) => s.classSection.class.name },
            { label: "Section", render: (s) => s.classSection.name },
            { label: "Exam", render: (s) => s.exam.name },
            {
              label: "Assigned At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
          ]}
          onView={(classSectionExam) => {
            setSelectedClassSectionExam(classSectionExam);
            setIsViewModalOpen(true);
          }}
        />
      )}

      {/* View Modal */}
      {isViewModalOpen && selectedClassSectionExam && (
        <ViewRecordModal
          title="Exam Assignment Details"
          subtitle={`${selectedClassSectionExam.classSection.class.name} - ${selectedClassSectionExam.classSection.name}`}
          record={selectedClassSectionExam}
          columns={[
            { label: "Class", render: (s) => s.classSection.class.name },
            { label: "Section", render: (s) => s.classSection.name },
            { label: "Exam", render: (s) => s.exam.name },
            {
              label: "Exam Start Date",
              render: (s) => formatDateInLocalFormat(s.exam.startDate),
            },
            {
              label: "Exam End Date",
              render: (s) => formatDateInLocalFormat(s.exam.endDate),
            },
            {
              label: "Assigned At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
          ]}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedClassSectionExam(null);
          }}
        />
      )}
    </ResourcePage>
  );
};
