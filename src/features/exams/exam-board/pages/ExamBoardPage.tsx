import { useMemo } from "react";
import {
  AcademicCapIcon,
  CalendarIcon,
  ChartBarIcon,
  DocumentCheckIcon,
} from "@heroicons/react/24/outline";

import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { RequiresResource } from "@/common/components/RequiresResourceCard";
import { useExams } from "../../create-exams/services/exam-query";
import { useClassSectionsForExam } from "../../class-section-exams/services/class-section-exams-query";

import { ExamStatsCard } from "../components/ExamStatsCard";
import { UpcomingExamsCard } from "../components/UpcomingExamsCard";
import { ExamCalendarView } from "../components/ExamCalendarView";
import { RecentExamResults } from "../components/RecentExamResults";
import { ExamProgressChart } from "../components/ExamProgressChart";

export const ExamBoardPage = () => {
  const { activeAcademicSession } = useAcademicSessionStore();

  // Fetch data
  const { data: examsData, isLoading: examsLoading } = useExams(
    activeAcademicSession?.id
  );

  // Get all exam schedules for the most recent exam
  const recentExam = useMemo(() => {
    if (!examsData?.items.length) return null;
    return examsData.items.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];
  }, [examsData]);

  const { data: recentExamClassSections } = useClassSectionsForExam(
    recentExam?.id
  );

  // Get schedules for all class sections of the recent exam
  const allSchedules = useMemo(() => {
    if (!recentExamClassSections?.items) return [];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const schedules: any[] = [];
    recentExamClassSections.items.forEach(() => {
      // Note: We'll need to fetch schedules for each class section exam
      // For now, we'll use a placeholder structure
    });
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return schedules;
  }, [recentExamClassSections]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalExams = examsData?.items.length ?? 0;

    // These would be calculated from actual schedule data
    const upcomingExams = 0; // Count from schedules
    const completedExams = 0; // Count from schedules
    const totalStudentsEnrolled = 0; // Count from enrollments

    return {
      totalExams,
      upcomingExams,
      completedExams,
      totalStudentsEnrolled,
    };
  }, [examsData]);

  if (!activeAcademicSession) {
    return (
      <RequiresResource resourceName="Academic Session" createUrl="/sessions" />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-base-content">Exam Board</h1>
          <p className="text-base-content/70 mt-1">
            Comprehensive overview of examinations and results
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-base-content/60">Academic Session</div>
          <div className="font-medium text-base-content">
            {activeAcademicSession.name}
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ExamStatsCard
          title="Total Exams"
          value={stats.totalExams}
          subtitle="This academic session"
          icon={<AcademicCapIcon className="w-6 h-6" />}
          color="primary"
        />
        <ExamStatsCard
          title="Upcoming Exams"
          value={stats.upcomingExams}
          subtitle="Next 30 days"
          icon={<CalendarIcon className="w-6 h-6" />}
          color="warning"
        />
        <ExamStatsCard
          title="Completed Exams"
          value={stats.completedExams}
          subtitle="This month"
          icon={<DocumentCheckIcon className="w-6 h-6" />}
          color="success"
        />
        <ExamStatsCard
          title="Students Enrolled"
          value={stats.totalStudentsEnrolled}
          subtitle="Active enrollments"
          icon={<ChartBarIcon className="w-6 h-6" />}
          color="accent"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - 2/3 width */}
        <div className="lg:col-span-2 space-y-6">
          {/* Calendar View */}
          <ExamCalendarView schedules={allSchedules} isLoading={examsLoading} />

          {/* Progress Chart */}
          <ExamProgressChart
            schedules={allSchedules}
            isLoading={examsLoading}
          />
        </div>

        {/* Right Column - 1/3 width */}
        <div className="space-y-6">
          {/* Upcoming Exams */}
          <UpcomingExamsCard
            schedules={allSchedules}
            isLoading={examsLoading}
          />

          {/* Recent Results */}
          <RecentExamResults
            results={[]} // Will be populated with actual results
            isLoading={examsLoading}
          />
        </div>
      </div>

      {/* Recent Exam Details */}
      {recentExam && (
        <div className="card bg-base-100 shadow-lg border border-base-300">
          <div className="card-body p-6">
            <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
              <DocumentCheckIcon className="w-5 h-5 mr-2 text-primary" />
              Latest Exam: {recentExam.name}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="font-medium text-base-content/80">
                  Exam Period
                </h4>
                <div className="text-sm text-base-content/70">
                  <div className="flex items-center">
                    <CalendarIcon className="w-4 h-4 mr-2" />
                    Start: {new Date(recentExam.startDate).toLocaleDateString()}
                  </div>
                  <div className="flex items-center mt-1">
                    <CalendarIcon className="w-4 h-4 mr-2" />
                    End: {new Date(recentExam.endDate).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-base-content/80">
                  Classes Assigned
                </h4>
                <div className="text-2xl font-bold text-primary">
                  {recentExamClassSections?.items.length ?? 0}
                </div>
                <div className="text-xs text-base-content/60">
                  Class sections participating
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-base-content/80">Status</h4>
                <div className="flex flex-col space-y-1">
                  {new Date() < new Date(recentExam.startDate) && (
                    <span className="badge badge-warning">Upcoming</span>
                  )}
                  {new Date() >= new Date(recentExam.startDate) &&
                    new Date() <= new Date(recentExam.endDate) && (
                      <span className="badge badge-info">In Progress</span>
                    )}
                  {new Date() > new Date(recentExam.endDate) && (
                    <span className="badge badge-success">Completed</span>
                  )}
                </div>
              </div>
            </div>

            {recentExamClassSections?.items &&
              recentExamClassSections.items.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-base-content/80 mb-3">
                    Participating Classes
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {recentExamClassSections.items.map((classSectionExam) => (
                      <div
                        key={classSectionExam.id}
                        className="p-2 bg-base-200 rounded text-center text-sm"
                      >
                        {classSectionExam.classSection.class.name} -{" "}
                        {classSectionExam.classSection.name}
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="card bg-gradient-to-r from-primary/5 to-secondary/5 border border-primary/20">
        <div className="card-body p-6">
          <h3 className="card-title text-lg font-semibold mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="/exams" className="btn btn-outline btn-primary">
              <AcademicCapIcon className="w-4 h-4 mr-2" />
              Create Exam
            </a>
            <a
              href="/exams/assign-to-class-sections"
              className="btn btn-outline btn-secondary"
            >
              <DocumentCheckIcon className="w-4 h-4 mr-2" />
              Assign Classes
            </a>
            <a href="/exams/schedules" className="btn btn-outline btn-accent">
              <CalendarIcon className="w-4 h-4 mr-2" />
              Schedule Exams
            </a>
            <a href="/exams/results" className="btn btn-outline btn-info">
              <ChartBarIcon className="w-4 h-4 mr-2" />
              Enter Results
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
