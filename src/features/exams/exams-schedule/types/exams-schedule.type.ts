import type { ClassSection } from "@/features/classes/types/class.type";
import type { createExamScheduleSchema, updateExamScheduleSchema } from "../schema/exams-schedule.schema";
import { z } from "zod";
import type { Exam } from "../../create-exams/types/exam.type";
import type { Subject } from "@/features/subjects/types/subjects.type";

export type CreateExamSchedulePayload = z.infer<
  typeof createExamScheduleSchema
>;

export type UpdateExamSchedulePayload = z.infer<
  typeof updateExamScheduleSchema
>;

export interface ExamSchedule {
  date: string;
  id: string;
  exam: Exam
  startTime: Date;
  endTime: Date;
  subject: Subject
  totalMarks: number;
  passingMarks: number;
  classSection: ClassSection
}
