import {
  dateSchema,
  dateTimeSchema,
} from "@/common/schemas/zod-common.schemas";
import { convertTimeInputToDateTime } from "@/utils/utils";
import { z } from "zod";

export const examScheduleBaseSchema = z.object({
  date: dateSchema,
  startTime: z.string().nonempty().transform((val) => convertTimeInputToDateTime(val)),
  endTime: z.string().nonempty().transform((val) => convertTimeInputToDateTime(val)),
  sectionSubjectId: z.string(),
  totalMarks: z.number().positive().max(1000, {
    message: "Total marks cannot exceed 1000",
  }),
  passingMarks: z.number().positive().max(1000, {
    message: "Passing marks cannot exceed 1000",
  }),
});

export const createExamScheduleSchema = examScheduleBaseSchema.refine(
  (data) => {
    return data.startTime < data.endTime;
  },
  {
    message: "Start time must be before end time",
  }
);

export const updateExamScheduleSchema = examScheduleBaseSchema.partial().refine(
  (data) => {
    // Only validate time constraint if both times are provided
    if (data.startTime && data.endTime) {
      return data.startTime < data.endTime;
    }
    return true;
  },
  {
    message: "Start time must be before end time",
  }
);
