import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createExamSchedule,
  fetchAllExamSchedules,
  updateExamSchedule,
} from "./exams-schedule.service";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";

const getExamSchedulesQueryKey = (classSectionExamId?: string) =>
  classSectionExamId ? ["exam-schedules", classSectionExamId] : ["exam-schedules"];

export function useExamSchedules(classSectionExamId?: string) {
  return useQuery({
    queryKey: getExamSchedulesQueryKey(classSectionExamId),
    queryFn: async () => {
      if (classSectionExamId) {
        return fetchAllExamSchedules(classSectionExamId);
      }
    },
    enabled: Boolean(classSectionExamId),
  });
}

export function useCreateExamSchedule(classSectionExamId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createExamSchedule,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam schedule", "create");
      await queryClient.invalidateQueries({
        queryKey: getExamSchedulesQueryKey(classSectionExamId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}

export function useUpdateExamSchedule(classSectionExamId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateExamSchedule,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam schedule", "update");
      await queryClient.invalidateQueries({
        queryKey: getExamSchedulesQueryKey(classSectionExamId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
