import { logger } from "@/lib/logger";
import type {
  CreateExamSchedulePayload,
  ExamSchedule,
  UpdateExamSchedulePayload,
} from "../types/exams-schedule.type";
import { sendApiRequest } from "@/common/services/api.service";
import type { PaginatedApiResponse } from "@/common/types/global.types";

interface CreateExamScheduleParams {
  classSectionExamId: string;
  payload: CreateExamSchedulePayload;
}

export async function createExamSchedule({
  classSectionExamId,
  payload,
}: CreateExamScheduleParams) {
  try {
    return await sendApiRequest(
      `/class-section-exams/${classSectionExamId}/schedules`,
      {
        method: "POST",
        withAuthorization: true,
        data: payload,
      }
    );
  } catch (error: unknown) {
    logger.error("Error creating exam schedule", error);
    throw error;
  }
}

interface UpdateExamScheduleParams {
  classSectionExamId: string;
  examScheduleId: string;
  payload: UpdateExamSchedulePayload;
}

export async function updateExamSchedule({
  classSectionExamId,
  examScheduleId,
  payload,
}: UpdateExamScheduleParams) {
  try {
    return await sendApiRequest(
      `/class-section-exams/${classSectionExamId}/schedules/${examScheduleId}`,
      {
        method: "PATCH",
        withAuthorization: true,
        data: payload,
      }
    );
  } catch (error: unknown) {
    logger.error("Error updating exam schedule", error);
    throw error;
  }
}

export async function fetchAllExamSchedules(classSectionExamId: string) {
  try {
    return await sendApiRequest<PaginatedApiResponse<ExamSchedule>>(
      `/class-section-exams/${classSectionExamId}/schedules`,
      {
        method: "GET",
        withAuthorization: true,
      }
    );
  } catch (error: unknown) {
    logger.error("Error fetching exam schedules", error);
    throw error;
  }
}
