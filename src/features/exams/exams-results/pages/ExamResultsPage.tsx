import { useState, useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { InputField } from "@/common/components/ui/form/InputField";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { RequiresResource } from "@/common/components/RequiresResourceCard";
import { notify } from "@/lib/notify";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import {
  formatDateInLocalFormat,
  removeEmptyFieldsFromObject,
} from "@/utils/utils";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";
import { ResourcePage } from "@/common/components/ResourcePage";
import type {
  CreateExamResultPayload,
  ExamResult,
  UpdateExamResultPayload,
} from "../types/exam-results.type";
import {
  useCreateExamResult,
  useExamResults,
  useUpdateExamResult,
} from "../services/exam-result-query";
import {
  createExamResultSchema,
  updateExamResultSchema,
} from "../schemas/exam-results.schema";
import { EXAM_RESULT_FORM_DEFAULT_VALUES } from "../constants/exam-result-constants";
import { useExams } from "../../create-exams/services/exam-query";
import { useClassSectionsForExam } from "../../class-section-exams/services/class-section-exams-query";
import { useExamSchedules } from "../../exams-schedule/service/exams-schedule-query";
import { useEnrollments } from "@/features/students/hooks/useEnrollements";
import type { Exam } from "../../create-exams/types/exam.type";
import type { ClassSectionExam } from "../../class-section-exams/types/class-section-exams.type";
import type { ExamSchedule } from "../../exams-schedule/types/exams-schedule.type";
import { logger } from "@/lib/logger";

export const ExamResultsPage = () => {
  // State for pagination
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  // State for form mode and view modal
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedExamResult, setSelectedExamResult] =
    useState<ExamResult | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // State for selections
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [selectedClassSectionExam, setSelectedClassSectionExam] =
    useState<ClassSectionExam | null>(null);
  const [selectedExamSchedule, setSelectedExamSchedule] =
    useState<ExamSchedule | null>(null);

  // Get active academic session
  const { activeAcademicSession } = useAcademicSessionStore();

  // Fetch data
  const { data: examsData } = useExams(activeAcademicSession?.id);
  const { data: classSectionExamsData } = useClassSectionsForExam(
    selectedExam?.id
  );
  const { data: examSchedulesData } = useExamSchedules(
    selectedClassSectionExam?.id
  );
  const { data: enrollmentsData } = useEnrollments(activeAcademicSession?.id, {
    sectionId: selectedClassSectionExam?.classSection.id,
    status: "ACTIVE",
  });
  const { data: examResultsData, isLoading } = useExamResults(
    selectedExamSchedule?.id,
    paginationParams
  );

  // Mutations for create and update
  const createExamResultMutation = useCreateExamResult(
    selectedExamSchedule?.id
  );
  const updateExamResultMutation = useUpdateExamResult(
    selectedExamSchedule?.id
  );

  // Determine which schema to use based on mode
  const schema = isEditMode ? updateExamResultSchema : createExamResultSchema;
  type FormData = z.infer<typeof schema>;

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: EXAM_RESULT_FORM_DEFAULT_VALUES,
  });

  // Watch isAbsent to conditionally handle marks
  const watchedIsAbsent = watch("isAbsent");

  // Reset form when switching between add/edit modes
  useEffect(() => {
    if (isEditMode && selectedExamResult) {
      reset({
        enrollmentId: selectedExamResult.student.id, // Note: using student.id as enrollmentId
        marksObtained: selectedExamResult.marksObtained,
        remarks: selectedExamResult.remarks ?? "",
        isAbsent: selectedExamResult.isAbsent,
      });
    } else {
      reset(EXAM_RESULT_FORM_DEFAULT_VALUES);
    }
  }, [isEditMode, selectedExamResult, reset]);

  // Clear marks when student is marked absent
  useEffect(() => {
    if (watchedIsAbsent) {
      setValue("marksObtained", null);
    }
  }, [watchedIsAbsent, setValue]);

  // Handle form submission
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!activeAcademicSession) {
      notify.error("No active academic session found");
      return;
    }

    if (!selectedExamSchedule) {
      notify.error("Please select an exam schedule first");
      return;
    }

    try {
      if (isEditMode && selectedExamResult) {
        await updateExamResultMutation.mutateAsync({
          examScheduleId: selectedExamSchedule.id,
          resultId: selectedExamResult.id,
          payload: removeEmptyFieldsFromObject(data) as UpdateExamResultPayload,
        });
      } else {
        await createExamResultMutation.mutateAsync({
          examScheduleId: selectedExamSchedule.id,
          payload: removeEmptyFieldsFromObject(data) as CreateExamResultPayload,
        });
      }

      // Reset form and refresh data
      cancelEdit();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handleEdit = (examResult: ExamResult) => {
    setSelectedExamResult(examResult);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedExamResult(null);
    reset(EXAM_RESULT_FORM_DEFAULT_VALUES);
  };

  const handleExamChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const examId = e.target.value;
    const exam = examsData?.items.find((e) => e.id === examId);
    setSelectedExam(exam ?? null);
    setSelectedClassSectionExam(null);
    setSelectedExamSchedule(null);
  };

  const handleClassSectionExamChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const classSectionExamId = e.target.value;
    const classSectionExam = classSectionExamsData?.items.find(
      (cse) => cse.id === classSectionExamId
    );
    setSelectedClassSectionExam(classSectionExam ?? null);
    setSelectedExamSchedule(null);
  };

  const handleExamScheduleChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const examScheduleId = e.target.value;
    const examSchedule = examSchedulesData?.items.find(
      (es) => es.id === examScheduleId
    );
    setSelectedExamSchedule(examSchedule ?? null);
  };

  // Filter out students who already have results for this exam schedule
  const availableStudents =
    enrollmentsData?.items.filter(
      (enrollment) =>
        !examResultsData?.items.some(
          (result) => result.student.id === enrollment.student.id
        )
    ) ?? [];

  // If no active academic session, show message
  if (!activeAcademicSession) {
    return (
      <RequiresResource resourceName="Academic Session" createUrl="/sessions" />
    );
  }

  return (
    <ResourcePage>
      {/* Selection Controls */}
      <div className="bg-base-200 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-4">Select Exam Schedule</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <SelectField
            name="examSelection"
            onChange={handleExamChange}
            label="Select Exam"
            value={selectedExam?.id ?? ""}
            defaultOptionLabel="-- Select Exam --"
            options={examsData?.items.map((exam) => ({
              value: exam.id,
              label: exam.name,
            }))}
          />

          {
            <SelectField
              name="classSectionExamSelection"
              onChange={handleClassSectionExamChange}
              label="Select Class Section"
              value={selectedClassSectionExam?.id ?? ""}
              defaultOptionLabel="-- Select Class Section --"
              disabled={!selectedExam}
              options={classSectionExamsData?.items.map((cse) => ({
                value: cse.id,
                label: `${cse.classSection.class.name} - ${cse.classSection.name}`,
              }))}
            />
          }

          {
            <SelectField
              name="examScheduleSelection"
              onChange={handleExamScheduleChange}
              label="Select Subject Schedule"
              value={selectedExamSchedule?.id ?? ""}
              disabled={!selectedClassSectionExam}
              defaultOptionLabel="-- Select Subject --"
              options={examSchedulesData?.items.map((es) => ({
                value: es.id,
                label: `${es.subject.name} - ${formatDateInLocalFormat(es.date)}`,
              }))}
            />
          }
        </div>

        {selectedExamSchedule && (
          <div className="mt-4 p-3 bg-base-100 rounded-md">
            <h4 className="font-medium text-sm text-base-content/80 mb-2">
              Schedule Details:
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">Subject:</span>{" "}
                {selectedExamSchedule.subject.name}
              </div>
              <div>
                <span className="font-medium">Date:</span>{" "}
                {formatDateInLocalFormat(selectedExamSchedule.date)}
              </div>
              <div>
                <span className="font-medium">Total Marks:</span>{" "}
                {selectedExamSchedule.totalMarks}
              </div>
              <div>
                <span className="font-medium">Passing Marks:</span>{" "}
                {selectedExamSchedule.passingMarks}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Form for creating/editing exam results */}
      {
        <ResourceCreationForm
          isFormSubmitting={isSubmitting}
          isEditMode={isEditMode}
          onFormSubmit={handleSubmit(onSubmit)}
          onCancelEdit={cancelEdit}
          resourceLabel="Exam Result"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <SelectField
              register={register}
              name="enrollmentId"
              label="Select Student"
              defaultOptionLabel="-- Select Student --"
              disabled={isEditMode}
              options={
                isEditMode
                  ? [
                      {
                        value: selectedExamResult?.student.id ?? "",
                        label: selectedExamResult?.student.name ?? "",
                      },
                    ]
                  : availableStudents.map((enrollment) => ({
                      value: enrollment.id,
                      label: `${enrollment.student.name} (Roll: ${enrollment.student.rollNumber})`,
                    }))
              }
            />

            <div className="flex items-end pb-3 space-x-2">
              <input
                type="checkbox"
                {...register("isAbsent")}
                className="checkbox checkbox-primary"
              />
              <label className="text-sm font-medium">Mark as Absent</label>
            </div>
          </div>

          {!watchedIsAbsent && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                placeholder="85"
                register={register}
                name="marksObtained"
                label={`Marks Obtained (Max: ${selectedExamSchedule?.totalMarks})`}
                type="number"
                valueAsNumber
                errorMessage={fieldErrors.marksObtained?.message}
                max={selectedExamSchedule?.totalMarks}
                min={0}
              />

              <InputField
                placeholder="Good performance"
                register={register}
                name="remarks"
                label="Remarks (Optional)"
                errorMessage={fieldErrors.remarks?.message}
              />
            </div>
          )}

          {watchedIsAbsent && (
            <InputField
              placeholder="Reason for absence"
              register={register}
              name="remarks"
              label="Remarks"
              errorMessage={fieldErrors.remarks?.message}
            />
          )}
        </ResourceCreationForm>
      }

      {/* Exam Results Table */}
      {
        <ResourceListingTable
          records={examResultsData?.items ?? []}
          totalRecords={examResultsData?.total ?? 0}
          isDataLoading={isLoading}
          searchLabel="student"
          searchFieldValue={(record) => record.student.name}
          pagination={paginationParams}
          onPaginationChange={setPaginationParams}
          columns={[
            {
              label: "Student",
              render: (r) => `${r.student.name} (${r.student.rollNumber})`,
            },
            {
              label: "Marks",
              render: (r) =>
                r.isAbsent ? "Absent" : `${r.marksObtained}/${r.totalMarks}`,
            },
            {
              label: "Status",
              render: (r) => {
                if (r.isAbsent)
                  return <span className="badge badge-error">Absent</span>;
                const percentage =
                  ((r.marksObtained ?? 0) / r.totalMarks) * 100;
                const passed = (r.marksObtained ?? 0) >= r.passingMarks;
                return (
                  <span
                    className={`badge ${passed ? "badge-success" : "badge-warning"}`}
                  >
                    {passed ? "Pass" : "Fail"} ({percentage.toFixed(1)}%)
                  </span>
                );
              },
            },
            { label: "Remarks", render: (r) => r.remarks ?? "-" },
          ]}
          onView={(item) => {
            setSelectedExamResult(item);
            setIsViewModalOpen(true);
          }}
        />
      }

      {/* View Exam Result Modal */}
      {isViewModalOpen && selectedExamResult && (
        <ViewRecordModal
          title="Exam Result Details"
          subtitle={`${selectedExamResult.student.name} - ${selectedExamResult.subject}`}
          record={selectedExamResult}
          columns={[
            { label: "Student", render: (r) => r.student.name },
            { label: "Roll Number", render: (r) => r.student.rollNumber },
            { label: "Subject", render: (r) => r.subject },
            { label: "Date", render: (r) => formatDateInLocalFormat(r.date) },
            { label: "Total Marks", render: (r) => r.totalMarks },
            { label: "Passing Marks", render: (r) => r.passingMarks },
            {
              label: "Marks Obtained",
              render: (r) => (r.isAbsent ? "Absent" : r.marksObtained),
            },
            {
              label: "Percentage",
              render: (r) =>
                r.isAbsent
                  ? "N/A"
                  : `${(((r.marksObtained ?? 0) / r.totalMarks) * 100).toFixed(1)}%`,
            },
            {
              label: "Result",
              render: (r) => {
                if (r.isAbsent) return "Absent";
                return (r.marksObtained ?? 0) >= r.passingMarks
                  ? "Pass"
                  : "Fail";
              },
            },
            { label: "Remarks", render: (r) => r.remarks ?? "No remarks" },
          ]}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit(selectedExamResult);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedExamResult(null);
          }}
        />
      )}
    </ResourcePage>
  );
};
