import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createExam, fetchAllExams, updateExam } from "./exam.service";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import type { PaginationParams } from "@/common/types/global.types";

export interface ExamQueryParams extends PaginationParams {
  status?: "upcoming" | "ongoing" | "completed";
  startDate?: string;
  endDate?: string;
  classId?: string;
}

const getExamsQueryKey = (
  academicSessionId?: string,
  params?: ExamQueryParams
) =>
  params
    ? [
        "exams",
        academicSessionId,
        params.offset,
        params.limit,
        params.status,
        params.startDate,
        params.endDate,
        params.classId,
      ]
    : ["exams", academicSessionId];

export function useExams(academicSessionId?: string, params?: ExamQueryParams) {
  return useQuery({
    queryKey: getExamsQueryKey(academicSessionId, params),
    queryFn: async () => {
      if (academicSessionId) {
        return await fetchAllExams(academicSessionId, params);
      }
    },
    enabled: Boolean(academicSessionId),
  });
}

export function useCreateExam(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createExam,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam", "create");
      await queryClient.invalidateQueries({
        queryKey: getExamsQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}

export function useUpdateExam(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateExam,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam", "update");
      await queryClient.invalidateQueries({
        queryKey: getExamsQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
