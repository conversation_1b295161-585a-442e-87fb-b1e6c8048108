import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createExam, fetchAllExams, updateExam } from "./exam.service";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import type { PaginationParams } from "@/common/types/global.types";

const getExamsQueryKey = (
  academicSessionId?: string,
  paginationParams?: PaginationParams,
) =>
  paginationParams
    ? [
        "exams",
        academicSessionId,
        paginationParams.offset,
        paginationParams.limit,
      ]
    : ["exams", academicSessionId];

export function useExams(
  academicSessionId?: string,
  paginationParams?: PaginationParams,
) {
  return useQuery({
    queryKey: getExamsQueryKey(academicSessionId, paginationParams),
    queryFn: async () => {
      if (academicSessionId) {
        return await fetchAllExams(academicSessionId);
      }
    },
    enabled: <PERSON><PERSON><PERSON>(academicSessionId),
  });
}

export function useCreateExam(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createExam,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam", "create");
      await queryClient.invalidateQueries({
        queryKey: getExamsQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}

export function useUpdateExam(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateExam,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam", "update");
      await queryClient.invalidateQueries({
        queryKey: getExamsQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
