import { useQuery } from "@tanstack/react-query";
import { fetchStudentAttendance } from "../services/student-attendance.service";
import type { StudentAttendance } from "../types/student-attendance.type";
import type { PaginationParams } from "@/common/types/global.types";

interface UseStudentAttendanceParams extends Partial<PaginationParams> {
  sectionId?: string;
  status?: StudentAttendance["status"];
  date?: string;
}

export const getStudentAttendanceQueryKey = (
  sectionId?: string,
  status?: StudentAttendance["status"],
  date?: string,
  offset?: number,
  limit?: number
) => ["student-attendance", sectionId, status, date, offset, limit];

export const useStudentAttendance = ({
  sectionId,
  status,
  date,
  offset,
  limit,
}: UseStudentAttendanceParams) => {
  return useQuery({
    queryKey: getStudentAttendanceQueryKey(
      sectionId,
      status,
      date,
      offset,
      limit
    ),
    queryFn: () => {
      if (sectionId) {
        return fetchStudentAttendance({
          sectionId,
          status,
          date,
          offset,
          limit,
        });
      }
    },
    enabled: <PERSON><PERSON>an(sectionId),
  });
};
