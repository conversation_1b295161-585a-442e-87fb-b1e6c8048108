import { useState, useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
  XMarkIcon,
  ClockIcon,
  CheckIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

import { Button } from "@/common/components/ui/Button";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { InputField } from "@/common/components/ui/form/InputField";
import { updateStudentAttendanceSchema } from "../schema/updateStudentAttendance.schema";
import { useUpdateStudentAttendance } from "../hooks/useUpdateStudentAttendance.hook";
import type {
  StudentAttendance,
  UpdateStudentAttendancePayload,
} from "../types/student-attendance.type";
import { formatDateInLocalFormat } from "@/utils/utils";

interface UpdateAttendanceModalProps {
  attendance: StudentAttendance;
  isOpen: boolean;
  onClose: () => void;
  sectionId?: string;
}

type UpdateAttendanceFormData = UpdateStudentAttendancePayload;

export const UpdateAttendanceModal = ({
  attendance,
  isOpen,
  onClose,
  sectionId,
}: UpdateAttendanceModalProps) => {
  const [showTimeWarning, setShowTimeWarning] = useState(false);

  const updateAttendanceMutation = useUpdateStudentAttendance(sectionId);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<UpdateAttendanceFormData>({
    resolver: zodResolver(updateStudentAttendanceSchema),
    defaultValues: getUpdateAttendanceFormDefaultValues(attendance),
  });

  const watchedStatus = watch("status");
  const watchedCheckedInTime = watch("checkedInTime");

  // Reset form when attendance changes
  useEffect(() => {
    reset(getUpdateAttendanceFormDefaultValues(attendance));
  }, [attendance, reset]);

  // Show warning when status changes require time input
  useEffect(() => {
    const originalStatus = attendance.status;
    const newStatus = watchedStatus;

    // Show warning if changing to LATE or PRESENT and no check-in time
    if (newStatus && (newStatus === "LATE" || newStatus === "PRESENT")) {
      if (originalStatus !== newStatus && !watchedCheckedInTime) {
        setShowTimeWarning(true);
      } else {
        setShowTimeWarning(false);
      }
    } else {
      setShowTimeWarning(false);
    }
  }, [watchedStatus, watchedCheckedInTime, attendance.status]);

  // Auto-set current time when changing to LATE or PRESENT
  useEffect(() => {
    if (watchedStatus === "LATE" || watchedStatus === "PRESENT") {
      if (!watchedCheckedInTime) {
        const now = new Date().toISOString();
        setValue("checkedInTime", now);
      }
    }
  }, [watchedStatus, watchedCheckedInTime, setValue]);

  const onSubmit: SubmitHandler<UpdateAttendanceFormData> = async (data) => {
    try {
      await updateAttendanceMutation.mutateAsync({
        attendanceId: attendance.id,
        payload: data,
      });
      onClose();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to update attendance:", error);
    }
  };

  const statusOptions = [
    { value: "PRESENT", label: "Present" },
    { value: "ABSENT", label: "Absent" },
    { value: "LATE", label: "Late" },
    { value: "LEAVE", label: "Leave" },
  ];

  const getStatusBadge = (status: StudentAttendance["status"]) => {
    const statusConfig = {
      PRESENT: "badge-success",
      ABSENT: "badge-error",
      LATE: "badge-warning",
      LEAVE: "badge-info",
    };

    return (
      <span className={`badge badge-sm ${statusConfig[status]}`}>{status}</span>
    );
  };

  const formatTimeForInput = (timeString: string | null) => {
    if (!timeString) return "";
    // Convert ISO string to datetime-local format (YYYY-MM-DDTHH:MM)
    return new Date(timeString).toISOString().slice(0, 16);
  };

  const formatTimeForSubmission = (timeString: string) => {
    if (!timeString) return undefined;
    // Convert datetime-local format to ISO string
    return new Date(timeString).toISOString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-semi-transparent bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-base-100 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-base-300">
          <div className="flex items-center space-x-3">
            <ClockIcon className="w-6 h-6 text-primary" />
            <div>
              <h2 className="text-xl font-semibold text-base-content">
                Update Attendance
              </h2>
              <p className="text-sm text-base-content/70">
                {attendance.student.name} •{" "}
                {formatDateInLocalFormat(attendance.date)}
              </p>
            </div>
          </div>
          <button onClick={onClose} className="btn btn-ghost btn-sm btn-circle">
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Student Info */}
          <div className="bg-base-200 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Student:</span>{" "}
                {attendance.student.name}
              </div>
              <div>
                <span className="font-medium">Roll No:</span>{" "}
                {attendance.student.rollNumber}
              </div>
              <div>
                <span className="font-medium">Class:</span>{" "}
                {attendance.classSection.class.name}
              </div>
              <div>
                <span className="font-medium">Section:</span>{" "}
                {attendance.classSection.name}
              </div>
              <div>
                <span className="font-medium">Date:</span>{" "}
                {formatDateInLocalFormat(attendance.date)}
              </div>
              <div>
                <span className="font-medium">Current Status:</span>{" "}
                {getStatusBadge(attendance.status)}
              </div>
            </div>
          </div>

          {/* Status Warning */}
          {showTimeWarning && (
            <div className="alert alert-warning">
              <ExclamationTriangleIcon className="w-5 h-5" />
              <span>
                Changing status to {watchedStatus} requires check-in time.
                Current time has been auto-filled.
              </span>
            </div>
          )}

          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SelectField
              name="status"
              label="Attendance Status"
              value={watchedStatus ?? ""}
              options={statusOptions}
              register={register}
              errorMessage={errors.status?.message}
            />

            <div className="md:col-span-2">
              <InputField
                name="remarks"
                label="Remarks"
                placeholder="Add any remarks (optional)"
                register={register}
                errorMessage={errors.remarks?.message}
              />
            </div>

            {(watchedStatus === "PRESENT" || watchedStatus === "LATE") && (
              <div>
                <label className="block text-sm font-medium text-base-content mb-1">
                  Check-in Time
                </label>
                <input
                  type="datetime-local"
                  {...register("checkedInTime", {
                    setValueAs: formatTimeForSubmission,
                  })}
                  defaultValue={formatTimeForInput(attendance.checkedInTime)}
                  className="input input-bordered w-full"
                />
                {errors.checkedInTime && (
                  <p className="text-error text-sm mt-1">
                    {errors.checkedInTime.message}
                  </p>
                )}
              </div>
            )}

            {(watchedStatus === "PRESENT" || watchedStatus === "LATE") && (
              <div>
                <label className="block text-sm font-medium text-base-content mb-1">
                  Check-out Time (Optional)
                </label>
                <input
                  type="datetime-local"
                  {...register("checkedOutTime", {
                    setValueAs: formatTimeForSubmission,
                  })}
                  defaultValue={formatTimeForInput(attendance.checkedOutTime)}
                  className="input input-bordered w-full"
                />
                {errors.checkedOutTime && (
                  <p className="text-error text-sm mt-1">
                    {errors.checkedOutTime.message}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-base-300">
            <Button
              type="button"
              onClick={onClose}
              outline
              disabled={updateAttendanceMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateAttendanceMutation.isPending}
              className="flex items-center space-x-2"
            >
              {updateAttendanceMutation.isPending ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Updating...</span>
                </>
              ) : (
                <>
                  <CheckIcon className="w-4 h-4" />
                  <span>Update Attendance</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

function getUpdateAttendanceFormDefaultValues(
  attendance: StudentAttendance
): UpdateAttendanceFormData {
  return {
    status: attendance.status,
    remarks: attendance.remarks || "",
    checkedInTime: attendance.checkedInTime ?? undefined,
    checkedOutTime: attendance.checkedOutTime ?? undefined,
  };
}
