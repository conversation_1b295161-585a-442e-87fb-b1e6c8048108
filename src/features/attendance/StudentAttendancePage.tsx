import { useState } from "react";
import { motion } from "framer-motion";
import { ClockIcon, CalendarIcon } from "@heroicons/react/24/outline";

import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { useStudentAttendance } from "./hooks/useStudentAttendance.hook";
import { useClasses } from "@/features/classes/service/class-query";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { SelectField } from "@/common/components/ui/form/SelectField";
import type { StudentAttendance } from "./types/student-attendance.type";
import type { PaginationParams } from "@/common/types/global.types";
import { apiParams } from "@/common/constants/api-params.constant";
import { UpdateAttendanceModal } from "./components/UpdateAttendanceModal";
import { formatDateInLocalFormat } from "@/utils/utils";

interface AttendanceFilters {
  classId?: string;
  sectionId?: string;
  status?: StudentAttendance["status"];
  date?: string;
}

export const StudentAttendancePage = () => {
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  const [filters, setFilters] = useState<AttendanceFilters>({});
  const [selectedAttendance, setSelectedAttendance] =
    useState<StudentAttendance | null>(null);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData } = useClasses(activeAcademicSession?.id);

  const { data: attendanceData, isLoading } = useStudentAttendance({
    sectionId: filters.sectionId,
    status: filters.status,
    date: filters.date,
    offset: paginationParams.offset,
    limit: paginationParams.limit,
  });

  const handleClassChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const classId = e.target.value || undefined;
    setFilters((prev) => ({
      ...prev,
      classId,
      sectionId: undefined, // Reset section when class changes
    }));
  };

  const handleSectionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const sectionId = e.target.value || undefined;
    setFilters((prev) => ({ ...prev, sectionId }));
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const status = e.target.value as StudentAttendance["status"] | undefined;
    setFilters((prev) => ({ ...prev, status: status ?? undefined }));
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = e.target.value || undefined;
    setFilters((prev) => ({ ...prev, date }));
  };

  const getClassOptions = () => {
    return [
      ...(classesData?.items.map((cls) => ({
        value: cls.id,
        label: cls.name,
      })) ?? []),
    ];
  };

  const getSectionOptions = () => {
    if (!filters.classId) return [{ value: "", label: "Select Section" }];

    const selectedClass = classesData?.items.find(
      (cls) => cls.id === filters.classId
    );
    return [
      ...(selectedClass?.sections.map((section) => ({
        value: section.id,
        label: section.name,
      })) ?? []),
    ];
  };

  const attendanceStatusOptions = [
    { value: "", label: "All Status" },
    { value: "PRESENT", label: "Present" },
    { value: "ABSENT", label: "Absent" },
    { value: "LATE", label: "Late" },
    { value: "LEAVE", label: "Leave" },
  ];

  const getAttendanceStatusBadge = (status: StudentAttendance["status"]) => {
    const statusConfig = {
      PRESENT: "badge-success",
      ABSENT: "badge-error",
      LATE: "badge-warning",
      LEAVE: "badge-info",
    };

    return (
      <span className={`badge badge-sm ${statusConfig[status]}`}>{status}</span>
    );
  };

  const formatTime = (time: string | null) => {
    if (!time) return "N/A";
    return new Date(time).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Show message when no class/section is selected
  const showEmptyState = !filters.classId || !filters.sectionId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <ClockIcon className="w-8 h-8 text-primary" />
        <div>
          <h1 className="text-2xl font-bold text-base-content">
            Student Attendance
          </h1>
          <p className="text-base-content/70">
            View and manage student attendance records
          </p>
        </div>
      </div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-base-200 rounded-lg p-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <SelectField
            name="classId"
            label="Class"
            value={filters.classId ?? ""}
            options={getClassOptions()}
            onChange={handleClassChange}
          />

          <SelectField
            name="sectionId"
            label="Section"
            value={filters.sectionId ?? ""}
            options={getSectionOptions()}
            onChange={handleSectionChange}
            disabled={!filters.classId}
          />

          <SelectField
            name="status"
            label="Attendance Status"
            value={filters.status ?? ""}
            options={attendanceStatusOptions}
            onChange={handleStatusChange}
          />

          <div>
            <label className="block text-sm font-medium text-base-content mb-1">
              Date
            </label>
            <input
              type="date"
              value={filters.date ?? ""}
              onChange={handleDateChange}
              className="input input-bordered w-full"
            />
          </div>
        </div>
      </motion.div>

      {/* Content */}
      {showEmptyState ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-base-200 rounded-lg p-8 text-center"
        >
          <CalendarIcon className="w-16 h-16 text-base-content/40 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-base-content mb-2">
            Select Class and Section
          </h3>
          <p className="text-base-content/70">
            Please select a class and section to view student attendance
            records.
          </p>
        </motion.div>
      ) : (
        /* Attendance Table */
        <ResourceListingTable
          records={attendanceData?.items ?? []}
          totalRecords={attendanceData?.total ?? 0}
          isDataLoading={isLoading}
          searchLabel="student name"
          searchFieldValue={(record) => record.student.name}
          pagination={paginationParams}
          onPaginationChange={setPaginationParams}
          columns={[
            {
              label: "Student Name",
              render: (a) => a.student.name,
            },
            {
              label: "Roll No",
              render: (a) => a.student.rollNumber,
            },
            {
              label: "Date",
              render: (a) => formatDateInLocalFormat(a.date),
            },
            {
              label: "Status",
              render: (a) => getAttendanceStatusBadge(a.status),
            },
            {
              label: "Check In",
              render: (a) => formatTime(a.checkedInTime),
            },
            {
              label: "Check Out",
              render: (a) => formatTime(a.checkedOutTime),
            },
            {
              label: "Remarks",
              render: (a) => a.remarks || "N/A",
            },
          ]}
          onView={(item) => {
            setSelectedAttendance(item);
            setIsUpdateModalOpen(true);
          }}
          actionButtonText="Update"
        />
      )}

      {/* Update Attendance Modal */}
      {isUpdateModalOpen && selectedAttendance && (
        <UpdateAttendanceModal
          attendance={selectedAttendance}
          isOpen={isUpdateModalOpen}
          onClose={() => {
            setIsUpdateModalOpen(false);
            setSelectedAttendance(null);
          }}
          sectionId={filters.sectionId}
        />
      )}
    </div>
  );
};
